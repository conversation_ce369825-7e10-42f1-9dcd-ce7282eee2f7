#!/usr/bin/env python3
"""
DMSoko Installation Script
This script helps set up the DMSoko classified listing platform.
"""

import os
import sys
import subprocess
import json

def run_command(command, description=""):
    """Run a shell command and handle errors"""
    print(f"\n{'='*50}")
    print(f"Running: {description or command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr}")
        return False

def check_prerequisites():
    """Check if required software is installed"""
    print("Checking prerequisites...")
    
    # Check Python
    try:
        python_version = subprocess.check_output([sys.executable, "--version"], text=True).strip()
        print(f"✓ {python_version}")
    except:
        print("✗ Python not found")
        return False
    
    # Check Node.js
    try:
        node_version = subprocess.check_output(["node", "--version"], text=True).strip()
        print(f"✓ Node.js {node_version}")
    except:
        print("✗ Node.js not found")
        return False
    
    # Check if we're in a Frappe bench
    if not os.path.exists("sites") or not os.path.exists("apps"):
        print("✗ Not in a Frappe bench directory")
        return False
    
    print("✓ In Frappe bench directory")
    return True

def install_dmsoko():
    """Install DMSoko app"""
    print("\n" + "="*60)
    print("INSTALLING DMSOKO CLASSIFIED LISTING PLATFORM")
    print("="*60)
    
    if not check_prerequisites():
        print("\nPrerequisites not met. Please install required software and run from a Frappe bench.")
        return False
    
    # Get site name
    site_name = input("\nEnter site name (e.g., dmsoko.local): ").strip()
    if not site_name:
        print("Site name is required")
        return False
    
    # Check if site exists
    if not os.path.exists(f"sites/{site_name}"):
        create_site = input(f"Site '{site_name}' doesn't exist. Create it? (y/N): ").strip().lower()
        if create_site == 'y':
            if not run_command(f"bench new-site {site_name}", "Creating new site"):
                return False
        else:
            print("Site is required to continue")
            return False
    
    # Install app
    steps = [
        (f"bench --site {site_name} install-app dmsoko", "Installing DMSoko app"),
        (f"bench --site {site_name} migrate", "Running database migrations"),
        (f"bench --site {site_name} install-fixtures", "Installing default data"),
        ("bench build --app dmsoko", "Building frontend assets"),
    ]
    
    for command, description in steps:
        if not run_command(command, description):
            print(f"\nFailed at step: {description}")
            return False
    
    # Create admin user if needed
    create_admin = input("\nCreate admin user? (y/N): ").strip().lower()
    if create_admin == 'y':
        admin_email = input("Admin email: ").strip()
        admin_password = input("Admin password: ").strip()
        
        if admin_email and admin_password:
            admin_command = f'bench --site {site_name} add-user {admin_email} --first-name Admin --last-name User --password {admin_password} --user-type "System User"'
            run_command(admin_command, "Creating admin user")
            
            # Add admin roles
            role_command = f'bench --site {site_name} add-user-role {admin_email} "DMSoko Admin"'
            run_command(role_command, "Adding DMSoko Admin role")
    
    print("\n" + "="*60)
    print("INSTALLATION COMPLETE!")
    print("="*60)
    print(f"Frontend URL: http://{site_name}:8000/dmsoko")
    print(f"Admin URL: http://{site_name}:8000/desk")
    print("\nTo start the server, run: bench start")
    print("\nDefault categories and roles have been installed.")
    print("You can now create listings and start using DMSoko!")
    
    return True

def main():
    """Main installation function"""
    try:
        install_dmsoko()
    except KeyboardInterrupt:
        print("\n\nInstallation cancelled by user")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        return False

if __name__ == "__main__":
    main()
