## DMSoko - Classified Listing Platform

DMSoko is a modern classified listing platform built with Frappe Framework and Vue.js. It allows users to buy and sell items, post job listings, offer services, and more in a user-friendly marketplace environment.

## Features

### Core Features
- **User Registration & Authentication** - Secure user accounts with profile management
- **Listing Management** - Create, edit, and manage classified listings
- **Category System** - Hierarchical categories for better organization
- **Advanced Search & Filtering** - Powerful search with multiple filters
- **Messaging System** - Built-in messaging between buyers and sellers
- **Wishlist** - Save favorite listings for later
- **Image Management** - Multiple images per listing with primary image selection
- **Location-based Listings** - Location filtering and search
- **Featured Listings** - Promote listings for better visibility

### User Features
- User profiles with ratings and verification
- Personal dashboard for managing listings
- Message center for communications
- Wishlist management
- Listing statistics and analytics

### Admin Features
- Category management
- User moderation and verification
- Listing approval workflow
- System analytics and reporting

## Technology Stack

### Backend
- **Frappe Framework** - Python-based web framework
- **MariaDB** - Database management
- **Redis** - Caching and session management

### Frontend
- **Vue.js 3** - Progressive JavaScript framework
- **Vue Router** - Client-side routing
- **Pinia** - State management
- **Tailwind CSS** - Utility-first CSS framework
- **Axios** - HTTP client for API calls

## Project Structure

```
apps/dmsoko/
├── dmsoko/
│   ├── api/                    # API endpoints
│   │   ├── listings.py         # Listing-related APIs
│   │   ├── categories.py       # Category APIs
│   │   ├── users.py           # User management APIs
│   │   ├── messages.py        # Messaging APIs
│   │   ├── wishlist.py        # Wishlist APIs
│   │   └── search.py          # Search APIs
│   ├── listings/              # Listings module
│   │   └── doctype/
│   │       ├── listing/       # Main listing DocType
│   │       ├── category/      # Category DocType
│   │       └── listing_image/ # Listing images child table
│   ├── messaging/             # Messaging module
│   │   └── doctype/
│   │       ├── conversation/  # Conversation DocType
│   │       └── message/       # Message DocType
│   ├── user_management/       # User management module
│   │   └── doctype/
│   │       ├── user_profile/  # Extended user profile
│   │       └── wishlist/      # User wishlist
│   ├── public/               # Static assets
│   │   ├── css/
│   │   ├── js/
│   │   └── vue-app/          # Vue.js application
│   │       ├── components/   # Reusable components
│   │       ├── pages/        # Page components
│   │       ├── store/        # Pinia stores
│   │       └── router/       # Vue Router configuration
│   ├── www/                  # Web pages
│   │   ├── dmsoko.html       # Main SPA entry point
│   │   └── dmsoko.py         # Page controller
│   ├── fixtures/             # Initial data
│   │   ├── custom_role.json  # User roles
│   │   └── category.json     # Default categories
│   └── hooks.py              # App configuration
```

## Installation

### Prerequisites
- Frappe Framework (v14 or later)
- Node.js (v16 or later)
- Python 3.8+
- MariaDB/MySQL

### Setup Steps

1. **Create a new Frappe site** (if you don't have one):
   ```bash
   bench new-site dmsoko.local
   ```

2. **Install the DMSoko app**:
   ```bash
   bench get-app https://github.com/iphenelist/dmsoko
   bench --site dmsoko.local install-app dmsoko
   ```

3. **Set up the database**:
   ```bash
   bench --site dmsoko.local migrate
   ```

4. **Install fixtures** (default categories and roles):
   ```bash
   bench --site dmsoko.local install-fixtures
   ```

5. **Build assets**:
   ```bash
   bench build --app dmsoko
   ```

6. **Start the development server**:
   ```bash
   bench start
   ```

7. **Access the application**:
   - Frontend: `http://dmsoko.local:8000/dmsoko`
   - Admin: `http://dmsoko.local:8000/desk`

## Configuration

### User Roles
The app creates three custom roles:
- **DMSoko User** - Regular users who can post listings
- **DMSoko Moderator** - Can moderate content and users
- **DMSoko Admin** - Full administrative access

### Default Categories
The app includes a comprehensive category structure:
- Electronics (Smartphones, Laptops & Computers)
- Vehicles (Cars, Motorcycles)
- Real Estate (Houses, Apartments)
- Fashion & Beauty (Men's Fashion, Women's Fashion)
- Home & Garden (Furniture, Appliances)
- Sports & Recreation (Fitness Equipment, Outdoor Sports)
- Jobs (Full-time Jobs, Part-time Jobs)
- Services (Home Services, Professional Services)

## API Documentation

### Authentication
All API endpoints (except public ones) require authentication. Use Frappe's session-based authentication.

### Key Endpoints

#### Listings
- `GET /api/method/dmsoko.api.listings.get_listings` - Get listings with filters
- `GET /api/method/dmsoko.api.listings.get_listing` - Get single listing
- `POST /api/method/dmsoko.api.listings.create_listing` - Create new listing
- `PUT /api/method/dmsoko.api.listings.update_listing` - Update listing
- `DELETE /api/method/dmsoko.api.listings.delete_listing` - Delete listing

#### Categories
- `GET /api/method/dmsoko.api.categories.get_categories` - Get category tree
- `GET /api/method/dmsoko.api.categories.get_popular_categories` - Get popular categories

#### Search
- `GET /api/method/dmsoko.api.search.search_listings` - Advanced search
- `GET /api/method/dmsoko.api.search.get_search_suggestions` - Search suggestions

#### Users
- `POST /api/method/dmsoko.api.users.register_user` - User registration
- `GET /api/method/dmsoko.api.users.get_user_profile` - Get user profile
- `PUT /api/method/dmsoko.api.users.update_user_profile` - Update profile

#### Messaging
- `GET /api/method/dmsoko.api.messages.get_conversations` - Get user conversations
- `POST /api/method/dmsoko.api.messages.send_message` - Send message
- `POST /api/method/dmsoko.api.messages.start_conversation` - Start new conversation

#### Wishlist
- `GET /api/method/dmsoko.api.wishlist.get_wishlist` - Get user wishlist
- `POST /api/method/dmsoko.api.wishlist.add_to_wishlist` - Add to wishlist
- `DELETE /api/method/dmsoko.api.wishlist.remove_from_wishlist` - Remove from wishlist

## Development

### Frontend Development
The Vue.js frontend is located in `dmsoko/public/js/vue-app/`. For development:

1. **Component Structure**: Follow Vue 3 Composition API patterns
2. **State Management**: Use Pinia stores for global state
3. **Styling**: Use Tailwind CSS utility classes
4. **API Integration**: Use the provided Frappe call wrapper

### Backend Development
Follow Frappe Framework conventions:

1. **DocTypes**: Define in respective modules
2. **API Methods**: Use `@frappe.whitelist()` decorator
3. **Permissions**: Configure in DocType JSON files
4. **Validation**: Implement in DocType Python files

### Testing
```bash
# Run backend tests
bench --site dmsoko.local run-tests --app dmsoko

# Run frontend tests (if configured)
cd apps/dmsoko/dmsoko/public/js/vue-app
npm test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Join our community forum
- Email: <EMAIL>

## Roadmap

### Phase 1 (Current)
- ✅ Basic listing functionality
- ✅ User authentication
- ✅ Category system
- ✅ Search and filtering
- ✅ Messaging system
- ✅ Wishlist functionality

### Phase 2 (Planned)
- [ ] Payment integration
- [ ] Advanced user verification
- [ ] Mobile app (React Native)
- [ ] Push notifications
- [ ] Advanced analytics
- [ ] Multi-language support

### Phase 3 (Future)
- [ ] AI-powered recommendations
- [ ] Video listings
- [ ] Auction functionality
- [ ] Business listings
- [ ] API marketplace
- [ ] White-label solutions

## DMSoko

### Installation

You can install this app using the [bench](https://github.com/frappe/bench) CLI:

```bash
cd $PATH_TO_YOUR_BENCH
bench get-app $URL_OF_THIS_REPO --branch develop
bench install-app dmsoko
```

### Contributing

This app uses `pre-commit` for code formatting and linting. Please [install pre-commit](https://pre-commit.com/#installation) and enable it for this repository:

```bash
cd apps/dmsoko
pre-commit install
```

Pre-commit is configured to use the following tools for checking and formatting your code:

- ruff
- eslint
- prettier
- pyupgrade

### CI

This app can use GitHub Actions for CI. The following workflows are configured:

- CI: Installs this app and runs unit tests on every push to `develop` branch.
- Linters: Runs [Frappe Semgrep Rules](https://github.com/frappe/semgrep-rules) and [pip-audit](https://pypi.org/project/pip-audit/) on every pull request.


### License

mit
