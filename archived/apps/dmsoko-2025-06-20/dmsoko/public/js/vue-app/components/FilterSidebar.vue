<template>
  <div class="filter-sidebar bg-white rounded-lg shadow-sm border p-6">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
      <button
        v-if="hasActiveFilters"
        @click="clearAllFilters"
        class="text-sm text-blue-600 hover:text-blue-800"
      >
        Clear all
      </button>
    </div>

    <!-- Search -->
    <div class="mb-6">
      <label class="form-label">Search</label>
      <input
        v-model="localFilters.search"
        type="text"
        placeholder="Search listings..."
        class="form-control"
        @input="debouncedUpdate"
      />
    </div>

    <!-- Category -->
    <div class="mb-6">
      <label class="form-label">Category</label>
      <select v-model="localFilters.category" @change="updateFilters" class="form-control">
        <option value="">All Categories</option>
        <option
          v-for="category in categories"
          :key="category.name"
          :value="category.name"
        >
          {{ category.category_name }}
        </option>
      </select>
    </div>

    <!-- Location -->
    <div class="mb-6">
      <label class="form-label">Location</label>
      <input
        v-model="localFilters.location"
        type="text"
        placeholder="Enter location..."
        class="form-control"
        @input="debouncedUpdate"
      />
      
      <!-- Popular Locations -->
      <div v-if="popularLocations.length > 0" class="mt-2">
        <div class="text-xs text-gray-500 mb-2">Popular locations:</div>
        <div class="flex flex-wrap gap-1">
          <button
            v-for="location in popularLocations.slice(0, 5)"
            :key="location.location"
            @click="selectLocation(location.location)"
            class="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700"
          >
            {{ location.location }}
          </button>
        </div>
      </div>
    </div>

    <!-- Price Range -->
    <div class="mb-6">
      <label class="form-label">Price Range</label>
      <div class="grid grid-cols-2 gap-2">
        <input
          v-model="localFilters.min_price"
          type="number"
          placeholder="Min"
          class="form-control"
          @input="debouncedUpdate"
        />
        <input
          v-model="localFilters.max_price"
          type="number"
          placeholder="Max"
          class="form-control"
          @input="debouncedUpdate"
        />
      </div>
      
      <!-- Price Suggestions -->
      <div class="mt-2 flex flex-wrap gap-1">
        <button
          v-for="range in priceRanges"
          :key="range.label"
          @click="selectPriceRange(range)"
          class="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700"
        >
          {{ range.label }}
        </button>
      </div>
    </div>

    <!-- Listing Type -->
    <div class="mb-6">
      <label class="form-label">Listing Type</label>
      <select v-model="localFilters.listing_type" @change="updateFilters" class="form-control">
        <option value="">All Types</option>
        <option
          v-for="type in listingTypes"
          :key="type.listing_type"
          :value="type.listing_type"
        >
          {{ type.listing_type }} ({{ type.count }})
        </option>
      </select>
    </div>

    <!-- Condition -->
    <div class="mb-6">
      <label class="form-label">Condition</label>
      <select v-model="localFilters.condition" @change="updateFilters" class="form-control">
        <option value="">Any Condition</option>
        <option
          v-for="condition in conditions"
          :key="condition.condition"
          :value="condition.condition"
        >
          {{ condition.condition }} ({{ condition.count }})
        </option>
      </select>
    </div>

    <!-- Additional Filters -->
    <div class="mb-6">
      <label class="form-label">Additional Options</label>
      <div class="space-y-2">
        <label class="flex items-center">
          <input
            v-model="localFilters.featured_only"
            type="checkbox"
            class="mr-2"
            @change="updateFilters"
          />
          <span class="text-sm">Featured listings only</span>
        </label>
        <label class="flex items-center">
          <input
            v-model="localFilters.with_images"
            type="checkbox"
            class="mr-2"
            @change="updateFilters"
          />
          <span class="text-sm">With images only</span>
        </label>
      </div>
    </div>

    <!-- Date Range -->
    <div class="mb-6">
      <label class="form-label">Posted</label>
      <select v-model="localFilters.date_range" @change="handleDateRangeChange" class="form-control">
        <option value="">Any time</option>
        <option value="today">Today</option>
        <option value="week">This week</option>
        <option value="month">This month</option>
        <option value="custom">Custom range</option>
      </select>
      
      <!-- Custom Date Range -->
      <div v-if="localFilters.date_range === 'custom'" class="mt-2 space-y-2">
        <input
          v-model="localFilters.date_from"
          type="date"
          class="form-control"
          @change="updateFilters"
        />
        <input
          v-model="localFilters.date_to"
          type="date"
          class="form-control"
          @change="updateFilters"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { debounce } from 'lodash-es'

const props = defineProps({
  filters: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update-filters', 'clear-filters'])

// Data
const localFilters = ref({ ...props.filters })
const categories = ref([])
const popularLocations = ref([])
const listingTypes = ref([])
const conditions = ref([])
const priceStats = ref({})

// Computed
const hasActiveFilters = computed(() => {
  return Object.values(localFilters.value).some(value => 
    value !== '' && value !== null && value !== undefined && value !== false
  )
})

const priceRanges = computed(() => {
  const ranges = [
    { label: 'Under $100', min: 0, max: 100 },
    { label: '$100 - $500', min: 100, max: 500 },
    { label: '$500 - $1,000', min: 500, max: 1000 },
    { label: '$1,000 - $5,000', min: 1000, max: 5000 },
    { label: 'Over $5,000', min: 5000, max: null }
  ]
  
  if (priceStats.value.max_price) {
    const maxPrice = priceStats.value.max_price
    if (maxPrice > 10000) {
      ranges.push({ label: `Over $10,000`, min: 10000, max: null })
    }
  }
  
  return ranges
})

// Methods
const updateFilters = () => {
  emit('update-filters', { ...localFilters.value })
}

const debouncedUpdate = debounce(updateFilters, 500)

const clearAllFilters = () => {
  localFilters.value = {}
  emit('clear-filters')
}

const selectLocation = (location) => {
  localFilters.value.location = location
  updateFilters()
}

const selectPriceRange = (range) => {
  localFilters.value.min_price = range.min
  localFilters.value.max_price = range.max
  updateFilters()
}

const handleDateRangeChange = () => {
  const range = localFilters.value.date_range
  const today = new Date()
  
  switch (range) {
    case 'today':
      localFilters.value.date_from = today.toISOString().split('T')[0]
      localFilters.value.date_to = today.toISOString().split('T')[0]
      break
    case 'week':
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      localFilters.value.date_from = weekAgo.toISOString().split('T')[0]
      localFilters.value.date_to = today.toISOString().split('T')[0]
      break
    case 'month':
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      localFilters.value.date_from = monthAgo.toISOString().split('T')[0]
      localFilters.value.date_to = today.toISOString().split('T')[0]
      break
    case '':
      delete localFilters.value.date_from
      delete localFilters.value.date_to
      break
  }
  
  if (range !== 'custom') {
    updateFilters()
  }
}

const loadFilterOptions = async () => {
  try {
    const response = await window.frappe.call({
      method: 'dmsoko.api.search.get_search_filters'
    })
    
    if (response.message.success) {
      const data = response.message.data
      categories.value = data.categories
      popularLocations.value = data.locations
      listingTypes.value = data.listing_types
      conditions.value = data.conditions
      priceStats.value = data.price_stats
    }
  } catch (error) {
    console.error('Failed to load filter options:', error)
  }
}

// Watchers
watch(() => props.filters, (newFilters) => {
  localFilters.value = { ...newFilters }
}, { deep: true })

// Lifecycle
onMounted(() => {
  loadFilterOptions()
})
</script>

<style scoped>
.filter-sidebar {
  position: sticky;
  top: 2rem;
  max-height: calc(100vh - 4rem);
  overflow-y: auto;
}
</style>
