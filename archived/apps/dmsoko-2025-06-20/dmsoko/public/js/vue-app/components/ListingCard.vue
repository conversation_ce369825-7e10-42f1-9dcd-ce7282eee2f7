<template>
  <div 
    class="listing-card group cursor-pointer"
    :class="{ 'compact': compact }"
    @click="viewListing"
  >
    <!-- Image -->
    <div class="relative overflow-hidden">
      <img
        :src="listing.primary_image || '/assets/dmsoko/images/placeholder.jpg'"
        :alt="listing.title"
        class="listing-card-image group-hover:scale-105 transition-transform duration-300"
      />
      
      <!-- Featured Badge -->
      <div v-if="listing.featured" class="absolute top-2 left-2">
        <span class="badge badge-warning">
          <i class="fas fa-star mr-1"></i>
          Featured
        </span>
      </div>
      
      <!-- Wishlist Button -->
      <button
        v-if="!isOwner"
        @click.stop="toggleWishlist"
        class="absolute top-2 right-2 w-8 h-8 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full flex items-center justify-center transition-all"
        :class="{ 'text-red-500': inWishlist, 'text-gray-400': !inWishlist }"
      >
        <i :class="inWishlist ? 'fas fa-heart' : 'far fa-heart'"></i>
      </button>
    </div>

    <!-- Content -->
    <div class="listing-card-content">
      <!-- Title -->
      <h3 class="listing-card-title">{{ listing.title }}</h3>
      
      <!-- Price -->
      <div class="listing-card-price">
        <span v-if="listing.price">
          {{ formatPrice(listing.price, listing.currency) }}
        </span>
        <span v-else class="text-gray-500">Price on request</span>
      </div>
      
      <!-- Location and Date -->
      <div class="flex items-center justify-between text-sm text-gray-500 mt-2">
        <div class="listing-card-location">
          <i class="fas fa-map-marker-alt mr-1"></i>
          {{ listing.location }}
        </div>
        <div>
          {{ formatDate(listing.creation) }}
        </div>
      </div>
      
      <!-- Category and Type (for non-compact) -->
      <div v-if="!compact" class="flex items-center justify-between mt-3">
        <span class="badge badge-primary">{{ listing.category_name }}</span>
        <span class="text-xs text-gray-500">{{ listing.listing_type }}</span>
      </div>
      
      <!-- Views (for non-compact) -->
      <div v-if="!compact" class="flex items-center justify-between mt-2 text-xs text-gray-500">
        <span>
          <i class="fas fa-eye mr-1"></i>
          {{ listing.views_count }} views
        </span>
        <span v-if="listing.user_name">by {{ listing.user_name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../store/user.js'

const props = defineProps({
  listing: {
    type: Object,
    required: true
  },
  compact: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const userStore = useUserStore()

// Data
const inWishlist = ref(false)

// Computed
const isOwner = computed(() => {
  return userStore.user && userStore.user.name === props.listing.created_by_user
})

// Methods
const viewListing = () => {
  router.push(`/listing/${props.listing.name}`)
}

const formatPrice = (price, currency) => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  })
  return formatter.format(price)
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  })
}

const toggleWishlist = async () => {
  if (!userStore.isLoggedIn) {
    router.push('/login')
    return
  }

  try {
    const method = inWishlist.value 
      ? 'dmsoko.api.wishlist.remove_from_wishlist'
      : 'dmsoko.api.wishlist.add_to_wishlist'
    
    const response = await window.frappe.call({
      method: method,
      args: { listing_id: props.listing.name }
    })
    
    if (response.message.success) {
      inWishlist.value = !inWishlist.value
    }
  } catch (error) {
    console.error('Failed to toggle wishlist:', error)
  }
}

const checkWishlistStatus = async () => {
  if (!userStore.isLoggedIn) return
  
  try {
    const response = await window.frappe.call({
      method: 'dmsoko.api.wishlist.check_wishlist_status',
      args: { listing_id: props.listing.name }
    })
    
    if (response.message.success) {
      inWishlist.value = response.message.data.in_wishlist
    }
  } catch (error) {
    console.error('Failed to check wishlist status:', error)
  }
}

// Lifecycle
onMounted(() => {
  checkWishlistStatus()
})
</script>

<style scoped>
.listing-card.compact .listing-card-image {
  height: 150px;
}

.listing-card:not(.compact) .listing-card-image {
  height: 200px;
}
</style>
