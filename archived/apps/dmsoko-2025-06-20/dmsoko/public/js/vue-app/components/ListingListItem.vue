<template>
  <div class="listing-list-item bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow cursor-pointer" @click="viewListing">
    <div class="flex p-4">
      <!-- Image -->
      <div class="flex-shrink-0 w-32 h-24 mr-4">
        <img
          :src="listing.primary_image || '/assets/dmsoko/images/placeholder.jpg'"
          :alt="listing.title"
          class="w-full h-full object-cover rounded-lg"
        />
      </div>

      <!-- Content -->
      <div class="flex-1 min-w-0">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <!-- Title and Featured Badge -->
            <div class="flex items-center mb-2">
              <h3 class="text-lg font-semibold text-gray-900 truncate mr-2">
                {{ listing.title }}
              </h3>
              <span v-if="listing.featured" class="badge badge-warning">
                <i class="fas fa-star mr-1"></i>
                Featured
              </span>
            </div>

            <!-- Description -->
            <p class="text-gray-600 text-sm line-clamp-2 mb-3">
              {{ listing.description }}
            </p>

            <!-- Meta Information -->
            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500">
              <div class="flex items-center">
                <i class="fas fa-map-marker-alt mr-1"></i>
                {{ listing.location }}
              </div>
              <div class="flex items-center">
                <i class="fas fa-tag mr-1"></i>
                {{ listing.category_name }}
              </div>
              <div class="flex items-center">
                <i class="fas fa-clock mr-1"></i>
                {{ formatDate(listing.creation) }}
              </div>
              <div class="flex items-center">
                <i class="fas fa-eye mr-1"></i>
                {{ listing.views_count }} views
              </div>
            </div>
          </div>

          <!-- Price and Actions -->
          <div class="flex flex-col items-end ml-4">
            <!-- Price -->
            <div class="text-xl font-bold text-blue-600 mb-2">
              <span v-if="listing.price">
                {{ formatPrice(listing.price, listing.currency) }}
              </span>
              <span v-else class="text-gray-500 text-base">Price on request</span>
            </div>

            <!-- Listing Type -->
            <div class="text-sm text-gray-500 mb-3">
              {{ listing.listing_type }}
              <span v-if="listing.condition" class="ml-1">• {{ listing.condition }}</span>
            </div>

            <!-- Actions -->
            <div class="flex items-center space-x-2">
              <!-- Wishlist Button -->
              <button
                v-if="!isOwner"
                @click.stop="toggleWishlist"
                class="w-8 h-8 rounded-full border flex items-center justify-center transition-colors"
                :class="inWishlist ? 'bg-red-50 border-red-200 text-red-500' : 'bg-gray-50 border-gray-200 text-gray-400 hover:text-red-500'"
                :title="inWishlist ? 'Remove from wishlist' : 'Add to wishlist'"
              >
                <i :class="inWishlist ? 'fas fa-heart' : 'far fa-heart'"></i>
              </button>

              <!-- Message Button -->
              <button
                v-if="!isOwner && userStore.isLoggedIn"
                @click.stop="startConversation"
                class="w-8 h-8 rounded-full bg-blue-50 border border-blue-200 text-blue-500 hover:bg-blue-100 flex items-center justify-center transition-colors"
                title="Send message"
              >
                <i class="fas fa-envelope"></i>
              </button>

              <!-- View Button -->
              <button
                @click.stop="viewListing"
                class="px-3 py-1 bg-gray-900 text-white text-sm rounded hover:bg-gray-800 transition-colors"
              >
                View
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../store/user.js'

const props = defineProps({
  listing: {
    type: Object,
    required: true
  }
})

const router = useRouter()
const userStore = useUserStore()

// Data
const inWishlist = ref(false)

// Computed
const isOwner = computed(() => {
  return userStore.user && userStore.user.name === props.listing.created_by_user
})

// Methods
const viewListing = () => {
  router.push(`/listing/${props.listing.name}`)
}

const formatPrice = (price, currency) => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  })
  return formatter.format(price)
}

const formatDate = (date) => {
  const now = new Date()
  const listingDate = new Date(date)
  const diffTime = Math.abs(now - listingDate)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) {
    return 'Today'
  } else if (diffDays === 2) {
    return 'Yesterday'
  } else if (diffDays <= 7) {
    return `${diffDays - 1} days ago`
  } else {
    return listingDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: listingDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    })
  }
}

const toggleWishlist = async () => {
  if (!userStore.isLoggedIn) {
    router.push('/login')
    return
  }

  try {
    const method = inWishlist.value 
      ? 'dmsoko.api.wishlist.remove_from_wishlist'
      : 'dmsoko.api.wishlist.add_to_wishlist'
    
    const response = await window.frappe.call({
      method: method,
      args: { listing_id: props.listing.name }
    })
    
    if (response.message.success) {
      inWishlist.value = !inWishlist.value
    }
  } catch (error) {
    console.error('Failed to toggle wishlist:', error)
  }
}

const startConversation = () => {
  router.push({
    path: '/messages',
    query: { listing: props.listing.name, action: 'new' }
  })
}

const checkWishlistStatus = async () => {
  if (!userStore.isLoggedIn) return
  
  try {
    const response = await window.frappe.call({
      method: 'dmsoko.api.wishlist.check_wishlist_status',
      args: { listing_id: props.listing.name }
    })
    
    if (response.message.success) {
      inWishlist.value = response.message.data.in_wishlist
    }
  } catch (error) {
    console.error('Failed to check wishlist status:', error)
  }
}

// Lifecycle
onMounted(() => {
  checkWishlistStatus()
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
