<template>
  <div class="pagination-container">
    <!-- Results Info -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div class="text-sm text-gray-700 mb-4 sm:mb-0">
        Showing {{ startItem }} to {{ endItem }} of {{ totalItems }} results
      </div>
      
      <!-- Pagination Controls -->
      <nav class="flex items-center space-x-1">
        <!-- Previous Button -->
        <button
          @click="goToPage(currentPage - 1)"
          :disabled="currentPage === 1"
          class="pagination-btn"
          :class="{ 'disabled': currentPage === 1 }"
        >
          <i class="fas fa-chevron-left"></i>
          <span class="hidden sm:inline ml-1">Previous</span>
        </button>
        
        <!-- Page Numbers -->
        <div class="flex items-center space-x-1">
          <!-- First Page -->
          <button
            v-if="showFirstPage"
            @click="goToPage(1)"
            class="pagination-number"
            :class="{ 'active': currentPage === 1 }"
          >
            1
          </button>
          
          <!-- First Ellipsis -->
          <span v-if="showFirstEllipsis" class="pagination-ellipsis">...</span>
          
          <!-- Visible Pages -->
          <button
            v-for="page in visiblePages"
            :key="page"
            @click="goToPage(page)"
            class="pagination-number"
            :class="{ 'active': currentPage === page }"
          >
            {{ page }}
          </button>
          
          <!-- Last Ellipsis -->
          <span v-if="showLastEllipsis" class="pagination-ellipsis">...</span>
          
          <!-- Last Page -->
          <button
            v-if="showLastPage"
            @click="goToPage(totalPages)"
            class="pagination-number"
            :class="{ 'active': currentPage === totalPages }"
          >
            {{ totalPages }}
          </button>
        </div>
        
        <!-- Next Button -->
        <button
          @click="goToPage(currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="pagination-btn"
          :class="{ 'disabled': currentPage === totalPages }"
        >
          <span class="hidden sm:inline mr-1">Next</span>
          <i class="fas fa-chevron-right"></i>
        </button>
      </nav>
    </div>
    
    <!-- Page Size Selector -->
    <div class="mt-4 flex items-center justify-center sm:justify-end">
      <label class="text-sm text-gray-700 mr-2">Items per page:</label>
      <select
        :value="pageSize"
        @change="changePageSize"
        class="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="10">10</option>
        <option value="20">20</option>
        <option value="50">50</option>
        <option value="100">100</option>
      </select>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  totalPages: {
    type: Number,
    required: true
  },
  totalItems: {
    type: Number,
    required: true
  },
  pageSize: {
    type: Number,
    default: 20
  },
  maxVisiblePages: {
    type: Number,
    default: 5
  }
})

const emit = defineEmits(['page-change', 'page-size-change'])

// Computed
const startItem = computed(() => {
  return (props.currentPage - 1) * props.pageSize + 1
})

const endItem = computed(() => {
  return Math.min(props.currentPage * props.pageSize, props.totalItems)
})

const visiblePages = computed(() => {
  const pages = []
  const maxVisible = props.maxVisiblePages
  const current = props.currentPage
  const total = props.totalPages
  
  if (total <= maxVisible) {
    // Show all pages if total is less than max visible
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // Calculate start and end of visible range
    let start = Math.max(1, current - Math.floor(maxVisible / 2))
    let end = Math.min(total, start + maxVisible - 1)
    
    // Adjust start if end is at the boundary
    if (end === total) {
      start = Math.max(1, end - maxVisible + 1)
    }
    
    // Don't include first and last page in visible range if they're shown separately
    if (start === 1) {
      start = 2
    }
    if (end === total) {
      end = total - 1
    }
    
    for (let i = start; i <= end; i++) {
      pages.push(i)
    }
  }
  
  return pages
})

const showFirstPage = computed(() => {
  return props.totalPages > props.maxVisiblePages && !visiblePages.value.includes(1)
})

const showLastPage = computed(() => {
  return props.totalPages > props.maxVisiblePages && !visiblePages.value.includes(props.totalPages)
})

const showFirstEllipsis = computed(() => {
  return showFirstPage.value && visiblePages.value[0] > 2
})

const showLastEllipsis = computed(() => {
  return showLastPage.value && visiblePages.value[visiblePages.value.length - 1] < props.totalPages - 1
})

// Methods
const goToPage = (page) => {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
    emit('page-change', page)
  }
}

const changePageSize = (event) => {
  const newPageSize = parseInt(event.target.value)
  emit('page-size-change', newPageSize)
}
</script>

<style scoped>
.pagination-btn {
  @apply px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
}

.pagination-btn.disabled {
  @apply opacity-50 cursor-not-allowed hover:bg-white hover:text-gray-500;
}

.pagination-number {
  @apply w-10 h-10 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
}

.pagination-number.active {
  @apply bg-blue-600 text-white border-blue-600 hover:bg-blue-700 hover:text-white;
}

.pagination-ellipsis {
  @apply px-2 py-2 text-sm text-gray-500;
}

@media (max-width: 640px) {
  .pagination-number {
    @apply w-8 h-8 text-xs;
  }
  
  .pagination-btn {
    @apply px-2 py-2 text-xs;
  }
}
</style>
