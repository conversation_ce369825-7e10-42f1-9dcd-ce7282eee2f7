<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="text-center">
        <router-link to="/" class="inline-block">
          <img class="h-12 w-auto" src="/assets/dmsoko/images/logo.png" alt="DMSoko" />
        </router-link>
        <h2 class="mt-6 text-3xl font-bold text-gray-900">
          Create your account
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          Already have an account?
          <router-link to="/login" class="font-medium text-blue-600 hover:text-blue-500">
            Sign in here
          </router-link>
        </p>
      </div>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <!-- Error <PERSON> -->
        <div v-if="error" class="alert alert-error mb-6">
          {{ error }}
        </div>

        <form @submit.prevent="handleRegister" class="space-y-6">
          <!-- First Name -->
          <div>
            <label for="first_name" class="form-label">First Name</label>
            <input
              id="first_name"
              v-model="form.first_name"
              type="text"
              required
              class="form-control"
              :class="{ 'border-red-500': errors.first_name }"
              placeholder="Enter your first name"
            />
            <div v-if="errors.first_name" class="form-error">{{ errors.first_name }}</div>
          </div>

          <!-- Last Name -->
          <div>
            <label for="last_name" class="form-label">Last Name</label>
            <input
              id="last_name"
              v-model="form.last_name"
              type="text"
              class="form-control"
              :class="{ 'border-red-500': errors.last_name }"
              placeholder="Enter your last name (optional)"
            />
            <div v-if="errors.last_name" class="form-error">{{ errors.last_name }}</div>
          </div>

          <!-- Email -->
          <div>
            <label for="email" class="form-label">Email address</label>
            <input
              id="email"
              v-model="form.email"
              type="email"
              required
              class="form-control"
              :class="{ 'border-red-500': errors.email }"
              placeholder="Enter your email"
            />
            <div v-if="errors.email" class="form-error">{{ errors.email }}</div>
          </div>

          <!-- Phone Number -->
          <div>
            <label for="phone_number" class="form-label">Phone Number</label>
            <input
              id="phone_number"
              v-model="form.phone_number"
              type="tel"
              class="form-control"
              :class="{ 'border-red-500': errors.phone_number }"
              placeholder="Enter your phone number (optional)"
            />
            <div v-if="errors.phone_number" class="form-error">{{ errors.phone_number }}</div>
          </div>

          <!-- Password -->
          <div>
            <label for="password" class="form-label">Password</label>
            <div class="relative">
              <input
                id="password"
                v-model="form.password"
                :type="showPassword ? 'text' : 'password'"
                required
                class="form-control pr-10"
                :class="{ 'border-red-500': errors.password }"
                placeholder="Create a password"
              />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'" class="text-gray-400"></i>
              </button>
            </div>
            <div v-if="errors.password" class="form-error">{{ errors.password }}</div>
            
            <!-- Password Strength Indicator -->
            <div class="mt-2">
              <div class="flex space-x-1">
                <div
                  v-for="i in 4"
                  :key="i"
                  class="h-1 flex-1 rounded"
                  :class="getPasswordStrengthColor(i)"
                ></div>
              </div>
              <p class="text-xs text-gray-500 mt-1">
                {{ passwordStrengthText }}
              </p>
            </div>
          </div>

          <!-- Confirm Password -->
          <div>
            <label for="confirm_password" class="form-label">Confirm Password</label>
            <input
              id="confirm_password"
              v-model="form.confirm_password"
              type="password"
              required
              class="form-control"
              :class="{ 'border-red-500': errors.confirm_password }"
              placeholder="Confirm your password"
            />
            <div v-if="errors.confirm_password" class="form-error">{{ errors.confirm_password }}</div>
          </div>

          <!-- Terms and Conditions -->
          <div>
            <label class="flex items-start">
              <input
                v-model="form.agree_terms"
                type="checkbox"
                required
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
              />
              <span class="ml-2 text-sm text-gray-900">
                I agree to the
                <a href="#" class="text-blue-600 hover:text-blue-500">Terms of Service</a>
                and
                <a href="#" class="text-blue-600 hover:text-blue-500">Privacy Policy</a>
              </span>
            </label>
            <div v-if="errors.agree_terms" class="form-error">{{ errors.agree_terms }}</div>
          </div>

          <!-- Marketing Emails -->
          <div>
            <label class="flex items-center">
              <input
                v-model="form.marketing_emails"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span class="ml-2 text-sm text-gray-900">
                Send me updates about new features and promotions
              </span>
            </label>
          </div>

          <!-- Submit Button -->
          <div>
            <button
              type="submit"
              :disabled="loading || !isFormValid"
              class="w-full btn-primary"
              :class="{ 'opacity-50 cursor-not-allowed': loading || !isFormValid }"
            >
              <span v-if="loading" class="spinner mr-2"></span>
              {{ loading ? 'Creating account...' : 'Create account' }}
            </button>
          </div>
        </form>

        <!-- Social Registration -->
        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300" />
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">Or register with</span>
            </div>
          </div>

          <div class="mt-6 grid grid-cols-2 gap-3">
            <button
              type="button"
              class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
            >
              <i class="fab fa-google text-red-500"></i>
              <span class="ml-2">Google</span>
            </button>

            <button
              type="button"
              class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
            >
              <i class="fab fa-facebook text-blue-600"></i>
              <span class="ml-2">Facebook</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../store/user.js'

const router = useRouter()
const userStore = useUserStore()

// Data
const loading = ref(false)
const showPassword = ref(false)
const error = ref('')

const form = reactive({
  first_name: '',
  last_name: '',
  email: '',
  phone_number: '',
  password: '',
  confirm_password: '',
  agree_terms: false,
  marketing_emails: true
})

const errors = reactive({
  first_name: '',
  last_name: '',
  email: '',
  phone_number: '',
  password: '',
  confirm_password: '',
  agree_terms: ''
})

// Computed
const passwordStrength = computed(() => {
  const password = form.password
  let strength = 0
  
  if (password.length >= 8) strength++
  if (/[a-z]/.test(password)) strength++
  if (/[A-Z]/.test(password)) strength++
  if (/[0-9]/.test(password)) strength++
  if (/[^A-Za-z0-9]/.test(password)) strength++
  
  return Math.min(strength, 4)
})

const passwordStrengthText = computed(() => {
  const texts = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong']
  return texts[passwordStrength.value] || 'Very Weak'
})

const isFormValid = computed(() => {
  return form.first_name && 
         form.email && 
         form.password && 
         form.confirm_password && 
         form.agree_terms &&
         form.password === form.confirm_password &&
         form.password.length >= 8
})

// Methods
const getPasswordStrengthColor = (index) => {
  if (index <= passwordStrength.value) {
    if (passwordStrength.value <= 1) return 'bg-red-500'
    if (passwordStrength.value <= 2) return 'bg-yellow-500'
    if (passwordStrength.value <= 3) return 'bg-blue-500'
    return 'bg-green-500'
  }
  return 'bg-gray-200'
}

const validateForm = () => {
  // Reset errors
  Object.keys(errors).forEach(key => errors[key] = '')
  
  let isValid = true
  
  if (!form.first_name.trim()) {
    errors.first_name = 'First name is required'
    isValid = false
  }
  
  if (!form.email.trim()) {
    errors.email = 'Email is required'
    isValid = false
  } else if (!form.email.includes('@')) {
    errors.email = 'Please enter a valid email address'
    isValid = false
  }
  
  if (!form.password) {
    errors.password = 'Password is required'
    isValid = false
  } else if (form.password.length < 8) {
    errors.password = 'Password must be at least 8 characters'
    isValid = false
  }
  
  if (!form.confirm_password) {
    errors.confirm_password = 'Please confirm your password'
    isValid = false
  } else if (form.password !== form.confirm_password) {
    errors.confirm_password = 'Passwords do not match'
    isValid = false
  }
  
  if (!form.agree_terms) {
    errors.agree_terms = 'You must agree to the terms and conditions'
    isValid = false
  }
  
  return isValid
}

const handleRegister = async () => {
  if (!validateForm()) return
  
  loading.value = true
  error.value = ''
  
  try {
    const result = await userStore.register({
      email: form.email,
      password: form.password,
      first_name: form.first_name,
      last_name: form.last_name,
      phone_number: form.phone_number
    })
    
    if (result.success) {
      // Redirect to login with success message
      router.push({
        path: '/login',
        query: { 
          registered: 'true',
          email: form.email
        }
      })
    } else {
      error.value = result.error || 'Registration failed. Please try again.'
    }
  } catch (err) {
    error.value = 'An unexpected error occurred. Please try again.'
    console.error('Registration error:', err)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* Component-specific styles */
</style>
