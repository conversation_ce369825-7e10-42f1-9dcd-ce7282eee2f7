<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md text-center">
      <!-- 404 Illustration -->
      <div class="mb-8">
        <div class="text-6xl text-gray-400 mb-4">
          <i class="fas fa-search"></i>
        </div>
        <h1 class="text-9xl font-bold text-gray-300">404</h1>
      </div>
      
      <!-- Error Message -->
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">
          Page Not Found
        </h2>
        <p class="text-gray-600 mb-6">
          Sorry, we couldn't find the page you're looking for. 
          It might have been moved, deleted, or you entered the wrong URL.
        </p>
      </div>
      
      <!-- Action Buttons -->
      <div class="space-y-4">
        <router-link
          to="/"
          class="w-full btn-primary inline-block"
        >
          <i class="fas fa-home mr-2"></i>
          Go to Homepage
        </router-link>
        
        <button
          @click="goBack"
          class="w-full btn-secondary"
        >
          <i class="fas fa-arrow-left mr-2"></i>
          Go Back
        </button>
        
        <router-link
          to="/listings"
          class="w-full btn-outline inline-block"
        >
          <i class="fas fa-list mr-2"></i>
          Browse Listings
        </router-link>
      </div>
      
      <!-- Search -->
      <div class="mt-8">
        <p class="text-sm text-gray-500 mb-4">
          Or search for what you're looking for:
        </p>
        <div class="flex">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search listings..."
            class="flex-1 form-control rounded-r-none"
            @keyup.enter="performSearch"
          />
          <button
            @click="performSearch"
            class="btn-primary rounded-l-none"
          >
            <i class="fas fa-search"></i>
          </button>
        </div>
      </div>
      
      <!-- Popular Categories -->
      <div class="mt-8">
        <p class="text-sm text-gray-500 mb-4">
          Popular categories:
        </p>
        <div class="flex flex-wrap justify-center gap-2">
          <router-link
            v-for="category in popularCategories"
            :key="category.name"
            :to="`/listings?category=${category.name}`"
            class="text-xs px-3 py-1 bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors"
          >
            {{ category.category_name }}
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// Data
const searchQuery = ref('')
const popularCategories = ref([])

// Methods
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

const performSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({
      path: '/listings',
      query: { search: searchQuery.value.trim() }
    })
  }
}

const loadPopularCategories = async () => {
  try {
    const response = await window.frappe.call({
      method: 'dmsoko.api.categories.get_popular_categories',
      args: { limit: 6 }
    })
    
    if (response.message.success) {
      popularCategories.value = response.message.data
    }
  } catch (error) {
    console.error('Failed to load categories:', error)
  }
}

// Lifecycle
onMounted(() => {
  loadPopularCategories()
})
</script>

<style scoped>
/* Component-specific styles */
</style>
