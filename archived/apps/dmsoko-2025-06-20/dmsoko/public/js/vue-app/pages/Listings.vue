<template>
  <div class="listings-page">
    <!-- <PERSON> Header -->
    <div class="bg-white border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">
              {{ pageTitle }}
            </h1>
            <p class="mt-1 text-gray-500">
              {{ totalListings }} listings found
            </p>
          </div>
          
          <!-- Sort Options -->
          <div class="mt-4 md:mt-0 flex items-center space-x-4">
            <select
              v-model="sortBy"
              @change="loadListings"
              class="form-select"
            >
              <option value="creation">Newest First</option>
              <option value="price">Price: Low to High</option>
              <option value="price_desc">Price: High to Low</option>
              <option value="views_count">Most Popular</option>
              <option value="title">Alphabetical</option>
            </select>
            
            <div class="flex border rounded-lg">
              <button
                @click="viewMode = 'grid'"
                :class="viewMode === 'grid' ? 'bg-blue-600 text-white' : 'text-gray-600'"
                class="px-3 py-2 rounded-l-lg"
              >
                <i class="fas fa-th-large"></i>
              </button>
              <button
                @click="viewMode = 'list'"
                :class="viewMode === 'list' ? 'bg-blue-600 text-white' : 'text-gray-600'"
                class="px-3 py-2 rounded-r-lg"
              >
                <i class="fas fa-list"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex flex-col lg:flex-row gap-8">
        <!-- Filters Sidebar -->
        <div class="lg:w-1/4">
          <FilterSidebar
            :filters="filters"
            @update-filters="updateFilters"
            @clear-filters="clearFilters"
          />
        </div>

        <!-- Main Content -->
        <div class="lg:w-3/4">
          <!-- Active Filters -->
          <div v-if="hasActiveFilters" class="mb-6">
            <div class="flex flex-wrap gap-2">
              <span
                v-for="(value, key) in activeFilters"
                :key="key"
                class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
              >
                {{ getFilterLabel(key, value) }}
                <button
                  @click="removeFilter(key)"
                  class="ml-2 text-blue-600 hover:text-blue-800"
                >
                  <i class="fas fa-times"></i>
                </button>
              </span>
              <button
                @click="clearFilters"
                class="text-sm text-gray-500 hover:text-gray-700 underline"
              >
                Clear all
              </button>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="loading" class="text-center py-12">
            <div class="spinner mx-auto mb-4"></div>
            <p class="text-gray-600">Loading listings...</p>
          </div>

          <!-- No Results -->
          <div v-else-if="listings.length === 0" class="text-center py-12">
            <div class="text-gray-400 mb-4">
              <i class="fas fa-search text-4xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">No listings found</h3>
            <p class="text-gray-600 mb-4">Try adjusting your search criteria or filters</p>
            <button
              @click="clearFilters"
              class="btn-primary"
            >
              Clear Filters
            </button>
          </div>

          <!-- Listings Grid/List -->
          <div v-else>
            <!-- Grid View -->
            <div
              v-if="viewMode === 'grid'"
              class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
            >
              <ListingCard
                v-for="listing in listings"
                :key="listing.name"
                :listing="listing"
              />
            </div>

            <!-- List View -->
            <div v-else class="space-y-4">
              <ListingListItem
                v-for="listing in listings"
                :key="listing.name"
                :listing="listing"
              />
            </div>

            <!-- Pagination -->
            <div v-if="totalPages > 1" class="mt-8">
              <Pagination
                :current-page="currentPage"
                :total-pages="totalPages"
                :total-items="totalListings"
                :page-size="pageSize"
                @page-change="changePage"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useListingsStore } from '../store/listings.js'
import ListingCard from '../components/ListingCard.vue'
import ListingListItem from '../components/ListingListItem.vue'
import FilterSidebar from '../components/FilterSidebar.vue'
import Pagination from '../components/Pagination.vue'

const route = useRoute()
const router = useRouter()
const listingsStore = useListingsStore()

// Data
const listings = ref([])
const loading = ref(true)
const viewMode = ref('grid')
const sortBy = ref('creation')
const currentPage = ref(1)
const totalListings = ref(0)
const totalPages = ref(0)
const pageSize = ref(20)
const filters = ref({})

// Computed
const pageTitle = computed(() => {
  if (route.query.search) {
    return `Search results for "${route.query.search}"`
  } else if (route.query.category) {
    return 'Category Listings'
  }
  return 'All Listings'
})

const activeFilters = computed(() => {
  const active = {}
  Object.keys(filters.value).forEach(key => {
    if (filters.value[key] && filters.value[key] !== '') {
      active[key] = filters.value[key]
    }
  })
  return active
})

const hasActiveFilters = computed(() => {
  return Object.keys(activeFilters.value).length > 0
})

// Methods
const loadListings = async () => {
  loading.value = true
  
  try {
    const searchFilters = { ...filters.value }
    
    // Add query parameters to filters
    if (route.query.search) {
      searchFilters.search = route.query.search
    }
    if (route.query.category) {
      searchFilters.category = route.query.category
    }
    
    const [sortField, sortOrder] = getSortParams()
    
    const response = await window.frappe.call({
      method: 'dmsoko.api.listings.get_listings',
      args: {
        filters: searchFilters,
        page: currentPage.value,
        page_size: pageSize.value,
        sort_by: sortField,
        sort_order: sortOrder
      }
    })
    
    if (response.message.success) {
      const data = response.message.data
      listings.value = data.listings
      totalListings.value = data.total
      totalPages.value = data.total_pages
    }
  } catch (error) {
    console.error('Failed to load listings:', error)
  } finally {
    loading.value = false
  }
}

const getSortParams = () => {
  switch (sortBy.value) {
    case 'price':
      return ['price', 'asc']
    case 'price_desc':
      return ['price', 'desc']
    case 'views_count':
      return ['views_count', 'desc']
    case 'title':
      return ['title', 'asc']
    default:
      return ['creation', 'desc']
  }
}

const updateFilters = (newFilters) => {
  filters.value = { ...newFilters }
  currentPage.value = 1
  loadListings()
  updateURL()
}

const clearFilters = () => {
  filters.value = {}
  currentPage.value = 1
  loadListings()
  updateURL()
}

const removeFilter = (filterKey) => {
  delete filters.value[filterKey]
  currentPage.value = 1
  loadListings()
  updateURL()
}

const changePage = (page) => {
  currentPage.value = page
  loadListings()
  updateURL()
  
  // Scroll to top
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

const updateURL = () => {
  const query = { ...route.query }
  
  // Add filters to query
  Object.keys(filters.value).forEach(key => {
    if (filters.value[key]) {
      query[key] = filters.value[key]
    } else {
      delete query[key]
    }
  })
  
  // Add pagination
  if (currentPage.value > 1) {
    query.page = currentPage.value
  } else {
    delete query.page
  }
  
  // Add sort
  if (sortBy.value !== 'creation') {
    query.sort = sortBy.value
  } else {
    delete query.sort
  }
  
  router.replace({ query })
}

const getFilterLabel = (key, value) => {
  const labels = {
    search: `Search: ${value}`,
    category: `Category: ${value}`,
    location: `Location: ${value}`,
    listing_type: `Type: ${value}`,
    condition: `Condition: ${value}`,
    min_price: `Min Price: $${value}`,
    max_price: `Max Price: $${value}`
  }
  return labels[key] || `${key}: ${value}`
}

const initializeFromQuery = () => {
  // Initialize filters from query parameters
  const queryFilters = {}
  
  if (route.query.search) queryFilters.search = route.query.search
  if (route.query.category) queryFilters.category = route.query.category
  if (route.query.location) queryFilters.location = route.query.location
  if (route.query.listing_type) queryFilters.listing_type = route.query.listing_type
  if (route.query.condition) queryFilters.condition = route.query.condition
  if (route.query.min_price) queryFilters.min_price = route.query.min_price
  if (route.query.max_price) queryFilters.max_price = route.query.max_price
  
  filters.value = queryFilters
  
  // Initialize pagination
  if (route.query.page) {
    currentPage.value = parseInt(route.query.page)
  }
  
  // Initialize sort
  if (route.query.sort) {
    sortBy.value = route.query.sort
  }
}

// Watchers
watch(() => route.query, () => {
  initializeFromQuery()
  loadListings()
}, { deep: true })

// Lifecycle
onMounted(() => {
  initializeFromQuery()
  loadListings()
})
</script>

<style scoped>
.listings-page {
  min-height: 100vh;
  background-color: #f9fafb;
}
</style>
