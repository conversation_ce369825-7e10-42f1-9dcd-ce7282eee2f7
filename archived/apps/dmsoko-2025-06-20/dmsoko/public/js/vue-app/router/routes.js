// Import page components
import Home from '../pages/Home.vue'
import Listings from '../pages/Listings.vue'
import ListingDetail from '../pages/ListingDetail.vue'
import PostListing from '../pages/PostListing.vue'
import Login from '../pages/Login.vue'
import Register from '../pages/Register.vue'
import Profile from '../pages/Profile.vue'
import MyListings from '../pages/MyListings.vue'
import Messages from '../pages/Messages.vue'
import Wishlist from '../pages/Wishlist.vue'
import Categories from '../pages/Categories.vue'

// Route guards
const requireAuth = (to, from, next) => {
  if (window.frappe.session.user === 'Guest') {
    next('/login')
  } else {
    next()
  }
}

const redirectIfAuth = (to, from, next) => {
  if (window.frappe.session.user !== 'Guest') {
    next('/')
  } else {
    next()
  }
}

// Define routes
const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { title: 'DMSoko - Your Marketplace' }
  },
  {
    path: '/listings',
    name: 'Listings',
    component: Listings,
    meta: { title: 'Browse Listings - DMSoko' }
  },
  {
    path: '/listing/:id',
    name: 'ListingDetail',
    component: ListingDetail,
    props: true,
    meta: { title: 'Listing Details - DMSoko' }
  },
  {
    path: '/categories',
    name: 'Categories',
    component: Categories,
    meta: { title: 'Categories - DMSoko' }
  },
  {
    path: '/post',
    name: 'PostListing',
    component: PostListing,
    beforeEnter: requireAuth,
    meta: { title: 'Post New Listing - DMSoko' }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    beforeEnter: redirectIfAuth,
    meta: { title: 'Login - DMSoko' }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    beforeEnter: redirectIfAuth,
    meta: { title: 'Sign Up - DMSoko' }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    beforeEnter: requireAuth,
    meta: { title: 'My Profile - DMSoko' }
  },
  {
    path: '/my-listings',
    name: 'MyListings',
    component: MyListings,
    beforeEnter: requireAuth,
    meta: { title: 'My Listings - DMSoko' }
  },
  {
    path: '/messages',
    name: 'Messages',
    component: Messages,
    beforeEnter: requireAuth,
    meta: { title: 'Messages - DMSoko' }
  },
  {
    path: '/wishlist',
    name: 'Wishlist',
    component: Wishlist,
    beforeEnter: requireAuth,
    meta: { title: 'My Wishlist - DMSoko' }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../pages/NotFound.vue'),
    meta: { title: 'Page Not Found - DMSoko' }
  }
]

export default routes
