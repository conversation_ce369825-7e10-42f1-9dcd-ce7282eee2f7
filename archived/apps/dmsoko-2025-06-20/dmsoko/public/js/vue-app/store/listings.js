import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useListingsStore = defineStore('listings', () => {
  // State
  const listings = ref([])
  const currentListing = ref(null)
  const categories = ref([])
  const loading = ref(false)
  const error = ref(null)
  const searchQuery = ref('')
  const selectedCategory = ref('')
  const priceRange = ref({ min: 0, max: 1000000 })
  const sortBy = ref('created_date')
  const sortOrder = ref('desc')

  // Getters
  const filteredListings = computed(() => {
    let filtered = [...listings.value]

    // Search filter
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      filtered = filtered.filter(listing => 
        listing.title.toLowerCase().includes(query) ||
        listing.description.toLowerCase().includes(query) ||
        listing.location.toLowerCase().includes(query)
      )
    }

    // Category filter
    if (selectedCategory.value) {
      filtered = filtered.filter(listing => 
        listing.category === selectedCategory.value
      )
    }

    // Price range filter
    filtered = filtered.filter(listing => 
      listing.price >= priceRange.value.min && 
      listing.price <= priceRange.value.max
    )

    // Sort
    filtered.sort((a, b) => {
      let aVal = a[sortBy.value]
      let bVal = b[sortBy.value]
      
      if (sortBy.value === 'price') {
        aVal = parseFloat(aVal) || 0
        bVal = parseFloat(bVal) || 0
      }
      
      if (sortOrder.value === 'asc') {
        return aVal > bVal ? 1 : -1
      } else {
        return aVal < bVal ? 1 : -1
      }
    })

    return filtered
  })

  const featuredListings = computed(() => {
    return listings.value.filter(listing => listing.featured).slice(0, 6)
  })

  const recentListings = computed(() => {
    return [...listings.value]
      .sort((a, b) => new Date(b.created_date) - new Date(a.created_date))
      .slice(0, 8)
  })

  // Actions
  const fetchListings = async (filters = {}) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.frappe.call({
        method: 'dmsoko.api.listings.get_listings',
        args: filters
      })

      if (response.message) {
        listings.value = response.message
      }
    } catch (err) {
      error.value = err.message || 'Failed to fetch listings'
      console.error('Failed to fetch listings:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchListing = async (listingId) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.frappe.call({
        method: 'dmsoko.api.listings.get_listing',
        args: { listing_id: listingId }
      })

      if (response.message) {
        currentListing.value = response.message
        return response.message
      }
    } catch (err) {
      error.value = err.message || 'Failed to fetch listing'
      console.error('Failed to fetch listing:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const createListing = async (listingData) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.frappe.call({
        method: 'dmsoko.api.listings.create_listing',
        args: listingData
      })

      if (response.message.success) {
        // Add to local listings array
        listings.value.unshift(response.message.listing)
        return { success: true, listing: response.message.listing }
      } else {
        throw new Error(response.message.error || 'Failed to create listing')
      }
    } catch (err) {
      error.value = err.message || 'Failed to create listing'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const updateListing = async (listingId, updateData) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.frappe.call({
        method: 'dmsoko.api.listings.update_listing',
        args: { listing_id: listingId, ...updateData }
      })

      if (response.message.success) {
        // Update local listing
        const index = listings.value.findIndex(l => l.name === listingId)
        if (index !== -1) {
          listings.value[index] = { ...listings.value[index], ...updateData }
        }
        
        if (currentListing.value && currentListing.value.name === listingId) {
          currentListing.value = { ...currentListing.value, ...updateData }
        }
        
        return { success: true }
      } else {
        throw new Error(response.message.error || 'Failed to update listing')
      }
    } catch (err) {
      error.value = err.message || 'Failed to update listing'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const deleteListing = async (listingId) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.frappe.call({
        method: 'dmsoko.api.listings.delete_listing',
        args: { listing_id: listingId }
      })

      if (response.message.success) {
        // Remove from local listings
        listings.value = listings.value.filter(l => l.name !== listingId)
        
        if (currentListing.value && currentListing.value.name === listingId) {
          currentListing.value = null
        }
        
        return { success: true }
      } else {
        throw new Error(response.message.error || 'Failed to delete listing')
      }
    } catch (err) {
      error.value = err.message || 'Failed to delete listing'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await window.frappe.call({
        method: 'dmsoko.api.categories.get_categories',
        args: {}
      })

      if (response.message) {
        categories.value = response.message
      }
    } catch (err) {
      console.error('Failed to fetch categories:', err)
    }
  }

  const searchListings = (query) => {
    searchQuery.value = query
  }

  const filterByCategory = (category) => {
    selectedCategory.value = category
  }

  const setPriceRange = (min, max) => {
    priceRange.value = { min, max }
  }

  const setSorting = (field, order = 'desc') => {
    sortBy.value = field
    sortOrder.value = order
  }

  const clearFilters = () => {
    searchQuery.value = ''
    selectedCategory.value = ''
    priceRange.value = { min: 0, max: 1000000 }
    sortBy.value = 'created_date'
    sortOrder.value = 'desc'
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    listings,
    currentListing,
    categories,
    loading,
    error,
    searchQuery,
    selectedCategory,
    priceRange,
    sortBy,
    sortOrder,
    
    // Getters
    filteredListings,
    featuredListings,
    recentListings,
    
    // Actions
    fetchListings,
    fetchListing,
    createListing,
    updateListing,
    deleteListing,
    fetchCategories,
    searchListings,
    filterByCategory,
    setPriceRange,
    setSorting,
    clearFilters,
    clearError
  }
})
