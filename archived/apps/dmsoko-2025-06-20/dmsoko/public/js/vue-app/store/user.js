import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // State
  const user = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // Getters
  const isLoggedIn = computed(() => {
    return user.value && window.frappe.session.user !== 'Guest'
  })

  const userFullName = computed(() => {
    return user.value?.full_name || user.value?.first_name || 'User'
  })

  // Actions
  const initializeSession = async () => {
    try {
      loading.value = true
      
      // Check if user is already logged in via Frappe session
      if (window.frappe.session.user !== 'Guest') {
        await fetchUserProfile()
      }
    } catch (err) {
      console.error('Failed to initialize session:', err)
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  const fetchUserProfile = async () => {
    try {
      const response = await window.frappe.call({
        method: 'dmsoko.api.users.get_user_profile',
        args: {}
      })
      
      if (response.message) {
        user.value = response.message
      }
    } catch (err) {
      console.error('Failed to fetch user profile:', err)
      throw err
    }
  }

  const login = async (email, password) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.frappe.call({
        method: 'login',
        args: {
          usr: email,
          pwd: password
        }
      })

      if (response.message === 'Logged In') {
        await fetchUserProfile()
        return { success: true }
      } else {
        throw new Error('Invalid credentials')
      }
    } catch (err) {
      error.value = err.message || 'Login failed'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const register = async (userData) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.frappe.call({
        method: 'dmsoko.api.users.register_user',
        args: userData
      })

      if (response.message.success) {
        // Auto-login after successful registration
        await login(userData.email, userData.password)
        return { success: true }
      } else {
        throw new Error(response.message.error || 'Registration failed')
      }
    } catch (err) {
      error.value = err.message || 'Registration failed'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    try {
      loading.value = true
      
      await window.frappe.call({
        method: 'logout'
      })
      
      user.value = null
      
      // Redirect to home page
      window.location.href = '/dmsoko'
    } catch (err) {
      console.error('Logout failed:', err)
    } finally {
      loading.value = false
    }
  }

  const updateProfile = async (profileData) => {
    try {
      loading.value = true
      error.value = null

      const response = await window.frappe.call({
        method: 'dmsoko.api.users.update_user_profile',
        args: profileData
      })

      if (response.message.success) {
        await fetchUserProfile()
        return { success: true }
      } else {
        throw new Error(response.message.error || 'Profile update failed')
      }
    } catch (err) {
      error.value = err.message || 'Profile update failed'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    user,
    loading,
    error,
    
    // Getters
    isLoggedIn,
    userFullName,
    
    // Actions
    initializeSession,
    fetchUserProfile,
    login,
    register,
    logout,
    updateProfile,
    clearError
  }
})
