import { createApp } from 'vue'
import { createRouter, createWeb<PERSON>istory } from 'vue-router'
import { createPinia } from 'pinia'
import App from './App.vue'
import routes from './router/routes.js'
import './style.css'

// Create router
const router = createRouter({
  history: createWebHistory('/dmsoko/'),
  routes
})

// Create pinia store
const pinia = createPinia()

// Create and mount app
const app = createApp(App)
app.use(router)
app.use(pinia)

// Global properties for Frappe integration
app.config.globalProperties.$frappe = window.frappe
app.config.globalProperties.$call = window.frappe.call

// Mount app when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    app.mount('#dmsoko-app')
  })
} else {
  app.mount('#dmsoko-app')
}

export default app
