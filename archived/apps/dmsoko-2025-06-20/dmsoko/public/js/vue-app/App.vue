<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- Navigation Header -->
    <nav class="bg-white shadow-sm border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <router-link to="/" class="flex items-center">
              <img class="h-8 w-auto" src="/assets/dmsoko/images/logo.png" alt="DMSoko" />
              <span class="ml-2 text-xl font-bold text-gray-900">DMSoko</span>
            </router-link>
          </div>
          
          <div class="flex items-center space-x-4">
            <router-link 
              to="/listings" 
              class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
            >
              Browse
            </router-link>
            <router-link 
              to="/post" 
              class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Post Ad
            </router-link>
            
            <!-- User Menu -->
            <div v-if="userStore.isLoggedIn" class="relative">
              <button @click="showUserMenu = !showUserMenu" class="flex items-center space-x-2">
                <img class="h-8 w-8 rounded-full" :src="userStore.user.user_image || '/assets/frappe/images/ui/avatar.png'" alt="" />
                <span class="text-sm font-medium text-gray-700">{{ userStore.user.full_name }}</span>
              </button>
              
              <div v-if="showUserMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                <router-link to="/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</router-link>
                <router-link to="/my-listings" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">My Listings</router-link>
                <router-link to="/messages" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Messages</router-link>
                <router-link to="/wishlist" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Wishlist</router-link>
                <hr class="my-1">
                <button @click="logout" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</button>
              </div>
            </div>
            
            <div v-else class="flex items-center space-x-2">
              <router-link to="/login" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Login</router-link>
              <router-link to="/register" class="bg-gray-900 hover:bg-gray-800 text-white px-4 py-2 rounded-md text-sm font-medium">Sign Up</router-link>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="flex-1">
      <router-view />
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 class="text-lg font-semibold mb-4">DMSoko</h3>
            <p class="text-gray-400">Your trusted marketplace for buying and selling.</p>
          </div>
          <div>
            <h4 class="text-md font-semibold mb-4">Quick Links</h4>
            <ul class="space-y-2 text-gray-400">
              <li><router-link to="/listings" class="hover:text-white">Browse Listings</router-link></li>
              <li><router-link to="/post" class="hover:text-white">Post Ad</router-link></li>
              <li><router-link to="/categories" class="hover:text-white">Categories</router-link></li>
            </ul>
          </div>
          <div>
            <h4 class="text-md font-semibold mb-4">Support</h4>
            <ul class="space-y-2 text-gray-400">
              <li><a href="#" class="hover:text-white">Help Center</a></li>
              <li><a href="#" class="hover:text-white">Contact Us</a></li>
              <li><a href="#" class="hover:text-white">Safety Tips</a></li>
            </ul>
          </div>
          <div>
            <h4 class="text-md font-semibold mb-4">Legal</h4>
            <ul class="space-y-2 text-gray-400">
              <li><a href="#" class="hover:text-white">Terms of Service</a></li>
              <li><a href="#" class="hover:text-white">Privacy Policy</a></li>
            </ul>
          </div>
        </div>
        <div class="mt-8 pt-8 border-t border-gray-800 text-center text-gray-400">
          <p>&copy; 2024 DMSoko. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from './store/user.js'

const userStore = useUserStore()
const showUserMenu = ref(false)

onMounted(() => {
  // Initialize user session
  userStore.initializeSession()
  
  // Close user menu when clicking outside
  document.addEventListener('click', (e) => {
    if (!e.target.closest('.relative')) {
      showUserMenu.value = false
    }
  })
})

const logout = async () => {
  await userStore.logout()
  showUserMenu.value = false
}
</script>

<style scoped>
/* Component-specific styles */
</style>
