/**
 * DMSoko JavaScript utilities and helpers
 */

// Initialize DMSoko namespace
window.DMSoko = window.DMSoko || {};

// Utility functions
DMSoko.utils = {
  /**
   * Format currency
   */
  formatCurrency: function(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  },

  /**
   * Format date
   */
  formatDate: function(date, options = {}) {
    const defaultOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    };
    return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options }).format(new Date(date));
  },

  /**
   * Format relative time (e.g., "2 hours ago")
   */
  formatRelativeTime: function(date) {
    const now = new Date();
    const past = new Date(date);
    const diffInSeconds = Math.floor((now - past) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return this.formatDate(date);
    }
  },

  /**
   * Debounce function
   */
  debounce: function(func, wait, immediate) {
    let timeout;
    return function executedFunction() {
      const context = this;
      const args = arguments;
      const later = function() {
        timeout = null;
        if (!immediate) func.apply(context, args);
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func.apply(context, args);
    };
  },

  /**
   * Throttle function
   */
  throttle: function(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  /**
   * Generate unique ID
   */
  generateId: function() {
    return Math.random().toString(36).substr(2, 9);
  },

  /**
   * Validate email
   */
  isValidEmail: function(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Validate phone number
   */
  isValidPhone: function(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  },

  /**
   * Sanitize HTML
   */
  sanitizeHtml: function(html) {
    const div = document.createElement('div');
    div.textContent = html;
    return div.innerHTML;
  },

  /**
   * Copy text to clipboard
   */
  copyToClipboard: function(text) {
    if (navigator.clipboard) {
      return navigator.clipboard.writeText(text);
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return Promise.resolve();
    }
  },

  /**
   * Get URL parameters
   */
  getUrlParams: function() {
    const params = new URLSearchParams(window.location.search);
    const result = {};
    for (const [key, value] of params) {
      result[key] = value;
    }
    return result;
  },

  /**
   * Set URL parameter
   */
  setUrlParam: function(key, value) {
    const url = new URL(window.location);
    url.searchParams.set(key, value);
    window.history.pushState({}, '', url);
  },

  /**
   * Remove URL parameter
   */
  removeUrlParam: function(key) {
    const url = new URL(window.location);
    url.searchParams.delete(key);
    window.history.pushState({}, '', url);
  }
};

// API helpers
DMSoko.api = {
  /**
   * Make API call to Frappe backend
   */
  call: function(method, args = {}) {
    return window.frappe.call({
      method: method,
      args: args
    });
  },

  /**
   * Upload file
   */
  uploadFile: function(file, doctype, docname, fieldname) {
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('doctype', doctype);
      formData.append('docname', docname);
      formData.append('fieldname', fieldname);

      const xhr = new XMLHttpRequest();
      xhr.open('POST', '/api/method/upload_file');
      xhr.setRequestHeader('X-Frappe-CSRF-Token', window.frappe.session.csrf_token);

      xhr.onload = function() {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (e) {
            reject(new Error('Invalid JSON response'));
          }
        } else {
          reject(new Error('HTTP ' + xhr.status + ': ' + xhr.statusText));
        }
      };

      xhr.onerror = function() {
        reject(new Error('Network error'));
      };

      xhr.send(formData);
    });
  }
};

// Notification helpers
DMSoko.notify = {
  /**
   * Show success notification
   */
  success: function(message, title = 'Success') {
    this.show(message, title, 'success');
  },

  /**
   * Show error notification
   */
  error: function(message, title = 'Error') {
    this.show(message, title, 'error');
  },

  /**
   * Show warning notification
   */
  warning: function(message, title = 'Warning') {
    this.show(message, title, 'warning');
  },

  /**
   * Show info notification
   */
  info: function(message, title = 'Info') {
    this.show(message, title, 'info');
  },

  /**
   * Show notification
   */
  show: function(message, title, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification`;
    notification.innerHTML = `
      <div class="d-flex justify-between items-center">
        <div>
          <strong>${title}</strong>
          <p class="mb-0">${message}</p>
        </div>
        <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()">
          <span>&times;</span>
        </button>
      </div>
    `;

    // Add to page
    let container = document.getElementById('notification-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'notification-container';
      container.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
      document.body.appendChild(container);
    }

    container.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);
  }
};

// Local storage helpers
DMSoko.storage = {
  /**
   * Set item in localStorage
   */
  set: function(key, value) {
    try {
      localStorage.setItem(`dmsoko_${key}`, JSON.stringify(value));
    } catch (e) {
      console.warn('Failed to save to localStorage:', e);
    }
  },

  /**
   * Get item from localStorage
   */
  get: function(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(`dmsoko_${key}`);
      return item ? JSON.parse(item) : defaultValue;
    } catch (e) {
      console.warn('Failed to read from localStorage:', e);
      return defaultValue;
    }
  },

  /**
   * Remove item from localStorage
   */
  remove: function(key) {
    try {
      localStorage.removeItem(`dmsoko_${key}`);
    } catch (e) {
      console.warn('Failed to remove from localStorage:', e);
    }
  },

  /**
   * Clear all DMSoko items from localStorage
   */
  clear: function() {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('dmsoko_')) {
          localStorage.removeItem(key);
        }
      });
    } catch (e) {
      console.warn('Failed to clear localStorage:', e);
    }
  }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  // Add any initialization code here
  console.log('DMSoko JavaScript loaded');
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DMSoko;
}
