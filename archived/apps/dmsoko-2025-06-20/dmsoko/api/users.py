# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import validate_email_address
import re


@frappe.whitelist()
def get_user_profile(user=None):
	"""Get user profile information"""
	try:
		if not user:
			user = frappe.session.user
		
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		# Check permissions
		if user != frappe.session.user and "DMSoko Admin" not in frappe.get_roles():
			return {"success": False, "error": "Permission denied"}
		
		# Get user document
		user_doc = frappe.get_doc("User", user)
		
		# Get or create user profile
		from dmsoko.user_management.doctype.user_profile.user_profile import UserProfile
		profile = UserProfile.get_or_create_profile(user)
		
		# Combine user and profile data
		user_data = profile.get_user_info()
		
		return {"success": True, "data": user_data}
		
	except frappe.DoesNotExistError:
		return {"success": Fals<PERSON>, "error": "User not found"}
	except Exception as e:
		frappe.log_error(f"Error in get_user_profile: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist()
def update_user_profile(**kwargs):
	"""Update user profile"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		user = frappe.session.user
		
		# Get or create user profile
		from dmsoko.user_management.doctype.user_profile.user_profile import UserProfile
		profile = UserProfile.get_or_create_profile(user)
		
		# Update allowed fields
		allowed_fields = [
			"phone_number", "whatsapp_number", "location", "address", "bio",
			"profile_image", "cover_image", "facebook_url", "twitter_url",
			"instagram_url", "linkedin_url", "email_notifications",
			"sms_notifications", "marketing_emails"
		]
		
		updated = False
		for key, value in kwargs.items():
			if key in allowed_fields and hasattr(profile, key):
				setattr(profile, key, value)
				updated = True
		
		if updated:
			profile.save()
			
			# Update last active
			profile.update_last_active()
		
		return {"success": True, "message": "Profile updated successfully"}
		
	except frappe.ValidationError as e:
		return {"success": False, "error": str(e)}
	except Exception as e:
		frappe.log_error(f"Error in update_user_profile: {str(e)}")
		return {"success": False, "error": "Failed to update profile"}


@frappe.whitelist(allow_guest=True)
def register_user(email, password, first_name, last_name=None, phone_number=None):
	"""Register new user"""
	try:
		# Validate email
		if not validate_email_address(email):
			return {"success": False, "error": "Invalid email address"}
		
		# Check if user already exists
		if frappe.db.exists("User", email):
			return {"success": False, "error": "User with this email already exists"}
		
		# Validate password
		if len(password) < 8:
			return {"success": False, "error": "Password must be at least 8 characters long"}
		
		# Create user
		user = frappe.get_doc({
			"doctype": "User",
			"email": email,
			"first_name": first_name,
			"last_name": last_name or "",
			"full_name": f"{first_name} {last_name or ''}".strip(),
			"new_password": password,
			"user_type": "Website User",
			"send_welcome_email": 0
		})
		
		# Add DMSoko User role
		user.add_roles("DMSoko User")
		user.insert(ignore_permissions=True)
		
		# Create user profile
		profile = frappe.get_doc({
			"doctype": "User Profile",
			"user": user.name,
			"phone_number": phone_number
		})
		profile.insert(ignore_permissions=True)
		
		# Send welcome email
		send_welcome_email(user.email, user.full_name)
		
		return {
			"success": True,
			"message": "User registered successfully",
			"data": {"user_id": user.name}
		}
		
	except frappe.ValidationError as e:
		return {"success": False, "error": str(e)}
	except Exception as e:
		frappe.log_error(f"Error in register_user: {str(e)}")
		return {"success": False, "error": "Registration failed"}


@frappe.whitelist()
def change_password(current_password, new_password):
	"""Change user password"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		# Validate current password
		user = frappe.get_doc("User", frappe.session.user)
		if not user.check_password(current_password):
			return {"success": False, "error": "Current password is incorrect"}
		
		# Validate new password
		if len(new_password) < 8:
			return {"success": False, "error": "New password must be at least 8 characters long"}
		
		# Update password
		user.new_password = new_password
		user.save(ignore_permissions=True)
		
		return {"success": True, "message": "Password changed successfully"}
		
	except Exception as e:
		frappe.log_error(f"Error in change_password: {str(e)}")
		return {"success": False, "error": "Failed to change password"}


@frappe.whitelist(allow_guest=True)
def reset_password(email):
	"""Send password reset email"""
	try:
		# Check if user exists
		if not frappe.db.exists("User", email):
			return {"success": False, "error": "User with this email does not exist"}
		
		# Generate reset key
		from frappe.utils import random_string
		reset_key = random_string(32)
		
		# Save reset key
		frappe.db.set_value("User", email, "reset_password_key", reset_key)
		frappe.db.commit()
		
		# Send reset email
		send_password_reset_email(email, reset_key)
		
		return {"success": True, "message": "Password reset email sent"}
		
	except Exception as e:
		frappe.log_error(f"Error in reset_password: {str(e)}")
		return {"success": False, "error": "Failed to send reset email"}


@frappe.whitelist()
def get_user_stats(user=None):
	"""Get user statistics"""
	try:
		if not user:
			user = frappe.session.user
		
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		# Check permissions
		if user != frappe.session.user and "DMSoko Admin" not in frappe.get_roles():
			return {"success": False, "error": "Permission denied"}
		
		# Get listing stats
		listing_stats = frappe.db.sql("""
			SELECT 
				COUNT(*) as total_listings,
				SUM(CASE WHEN status = 'Active' THEN 1 ELSE 0 END) as active_listings,
				SUM(CASE WHEN status = 'Sold' THEN 1 ELSE 0 END) as sold_listings,
				SUM(CASE WHEN status = 'Draft' THEN 1 ELSE 0 END) as draft_listings,
				SUM(views_count) as total_views
			FROM `tabListing`
			WHERE created_by_user = %s
		""", (user,), as_dict=True)[0]
		
		# Get message stats
		from dmsoko.messaging.doctype.conversation.conversation import Conversation
		conversation_stats = Conversation.get_conversation_stats(user)
		
		# Get wishlist count
		from dmsoko.user_management.doctype.wishlist.wishlist import Wishlist
		wishlist_count = Wishlist.get_wishlist_count(user)
		
		stats = {
			**listing_stats,
			**conversation_stats,
			"wishlist_count": wishlist_count
		}
		
		return {"success": True, "data": stats}
		
	except Exception as e:
		frappe.log_error(f"Error in get_user_stats: {str(e)}")
		return {"success": False, "error": str(e)}


def send_welcome_email(email, full_name):
	"""Send welcome email to new user"""
	try:
		frappe.sendmail(
			recipients=[email],
			subject="Welcome to DMSoko!",
			message=f"""
			<h2>Welcome to DMSoko, {full_name}!</h2>
			<p>Thank you for joining our marketplace community.</p>
			<p>You can now:</p>
			<ul>
				<li>Post free classified ads</li>
				<li>Browse thousands of listings</li>
				<li>Connect with buyers and sellers</li>
				<li>Save your favorite items to wishlist</li>
			</ul>
			<p><a href="{frappe.utils.get_url()}/dmsoko">Start exploring DMSoko</a></p>
			<p>Happy buying and selling!</p>
			<p>The DMSoko Team</p>
			"""
		)
	except Exception as e:
		frappe.log_error(f"Failed to send welcome email: {str(e)}")


def send_password_reset_email(email, reset_key):
	"""Send password reset email"""
	try:
		reset_url = f"{frappe.utils.get_url()}/dmsoko/reset-password?key={reset_key}"
		
		frappe.sendmail(
			recipients=[email],
			subject="Reset your DMSoko password",
			message=f"""
			<h2>Reset Your Password</h2>
			<p>You requested to reset your password for your DMSoko account.</p>
			<p>Click the link below to reset your password:</p>
			<p><a href="{reset_url}">Reset Password</a></p>
			<p>This link will expire in 24 hours.</p>
			<p>If you didn't request this, please ignore this email.</p>
			<p>The DMSoko Team</p>
			"""
		)
	except Exception as e:
		frappe.log_error(f"Failed to send password reset email: {str(e)}")
