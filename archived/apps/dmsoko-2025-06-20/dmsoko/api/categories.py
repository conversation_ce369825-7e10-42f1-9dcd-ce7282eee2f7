# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import cint


@frappe.whitelist(allow_guest=True)
def get_categories():
	"""Get all active categories in tree structure"""
	try:
		# Check cache first
		categories = frappe.cache().get_value("category_tree")
		
		if not categories:
			from dmsoko.listings.doctype.category.category import Category
			categories = Category.get_category_tree()
			
			# Cache for 1 hour
			frappe.cache().set_value("category_tree", categories, expires_in_sec=3600)
		
		return {"success": True, "data": categories}
		
	except Exception as e:
		frappe.log_error(f"Error in get_categories: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist(allow_guest=True)
def get_category(category_id):
	"""Get single category with details"""
	try:
		category = frappe.get_doc("Category", category_id)
		
		if not category.is_active:
			return {"success": False, "error": "Category not found"}
		
		category_data = category.as_dict()
		
		# Get children
		category_data["children"] = category.get_children()
		
		# Get breadcrumbs
		category_data["breadcrumbs"] = category.get_breadcrumbs()
		
		# Get listing count
		listing_count = frappe.db.count("Listing", {
			"category": category_id,
			"status": "Active",
			"expires_on": [">=", frappe.utils.today()]
		})
		category_data["listing_count"] = listing_count
		
		return {"success": True, "data": category_data}
		
	except frappe.DoesNotExistError:
		return {"success": False, "error": "Category not found"}
	except Exception as e:
		frappe.log_error(f"Error in get_category: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist(allow_guest=True)
def get_popular_categories(limit=10):
	"""Get popular categories based on listing count"""
	try:
		limit = cint(limit) or 10
		
		# Check cache first
		cache_key = f"popular_categories_{limit}"
		categories = frappe.cache().get_value(cache_key)
		
		if not categories:
			from dmsoko.listings.doctype.category.category import Category
			categories = Category.get_popular_categories(limit)
			
			# Cache for 30 minutes
			frappe.cache().set_value(cache_key, categories, expires_in_sec=1800)
		
		return {"success": True, "data": categories}
		
	except Exception as e:
		frappe.log_error(f"Error in get_popular_categories: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist()
def create_category(**kwargs):
	"""Create new category (Admin only)"""
	try:
		# Check permissions
		if "DMSoko Admin" not in frappe.get_roles() and "System Manager" not in frappe.get_roles():
			return {"success": False, "error": "Permission denied"}
		
		# Create category
		category = frappe.get_doc({
			"doctype": "Category",
			**kwargs
		})
		
		category.insert()
		
		# Clear cache
		frappe.cache().delete_key("category_tree")
		frappe.cache().delete_key("popular_categories")
		
		return {
			"success": True,
			"message": "Category created successfully",
			"data": {"category_id": category.name}
		}
		
	except frappe.ValidationError as e:
		return {"success": False, "error": str(e)}
	except Exception as e:
		frappe.log_error(f"Error in create_category: {str(e)}")
		return {"success": False, "error": "Failed to create category"}


@frappe.whitelist()
def update_category(category_id, **kwargs):
	"""Update category (Admin only)"""
	try:
		# Check permissions
		if "DMSoko Admin" not in frappe.get_roles() and "System Manager" not in frappe.get_roles():
			return {"success": False, "error": "Permission denied"}
		
		# Get category
		category = frappe.get_doc("Category", category_id)
		
		# Update fields
		for key, value in kwargs.items():
			if hasattr(category, key):
				setattr(category, key, value)
		
		category.save()
		
		# Clear cache
		frappe.cache().delete_key("category_tree")
		frappe.cache().delete_key("popular_categories")
		
		return {"success": True, "message": "Category updated successfully"}
		
	except frappe.DoesNotExistError:
		return {"success": False, "error": "Category not found"}
	except Exception as e:
		frappe.log_error(f"Error in update_category: {str(e)}")
		return {"success": False, "error": "Failed to update category"}


@frappe.whitelist()
def delete_category(category_id):
	"""Delete category (Admin only)"""
	try:
		# Check permissions
		if "DMSoko Admin" not in frappe.get_roles() and "System Manager" not in frappe.get_roles():
			return {"success": False, "error": "Permission denied"}
		
		# Get category
		category = frappe.get_doc("Category", category_id)
		category.delete()
		
		# Clear cache
		frappe.cache().delete_key("category_tree")
		frappe.cache().delete_key("popular_categories")
		
		return {"success": True, "message": "Category deleted successfully"}
		
	except frappe.DoesNotExistError:
		return {"success": False, "error": "Category not found"}
	except Exception as e:
		frappe.log_error(f"Error in delete_category: {str(e)}")
		return {"success": False, "error": "Failed to delete category"}


@frappe.whitelist(allow_guest=True)
def get_category_listings(category_id, page=1, page_size=20, sort_by="creation", sort_order="desc"):
	"""Get listings for a specific category"""
	try:
		page = cint(page) or 1
		page_size = cint(page_size) or 20
		
		# Get category and all its children
		category = frappe.get_doc("Category", category_id)
		category_ids = [category_id]
		
		# Add child categories
		children = category.get_all_children()
		category_ids.extend([child["name"] for child in children])
		
		# Get listings
		filters = {
			"category": ["in", category_ids],
			"status": "Active",
			"expires_on": [">=", frappe.utils.today()]
		}
		
		# Get total count
		total = frappe.db.count("Listing", filters)
		
		# Get listings
		fields = [
			"name", "title", "description", "price", "currency", "location", 
			"category", "listing_type", "condition", "creation", "views_count"
		]
		
		listings = frappe.get_all(
			"Listing",
			filters=filters,
			fields=fields,
			order_by=f"{sort_by} {sort_order}",
			start=(page - 1) * page_size,
			page_length=page_size
		)
		
		# Add primary images and category names
		for listing in listings:
			primary_image = frappe.db.get_value(
				"Listing Image",
				{"parent": listing.name, "is_primary": 1},
				"image"
			)
			listing["primary_image"] = primary_image
			listing["category_name"] = frappe.db.get_value("Category", listing.category, "category_name")
		
		return {
			"success": True,
			"data": {
				"listings": listings,
				"total": total,
				"page": page,
				"page_size": page_size,
				"total_pages": (total + page_size - 1) // page_size,
				"category": category.as_dict()
			}
		}
		
	except frappe.DoesNotExistError:
		return {"success": False, "error": "Category not found"}
	except Exception as e:
		frappe.log_error(f"Error in get_category_listings: {str(e)}")
		return {"success": False, "error": str(e)}
