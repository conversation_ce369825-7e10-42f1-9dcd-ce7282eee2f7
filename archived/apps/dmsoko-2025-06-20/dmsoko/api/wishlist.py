# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import cint


@frappe.whitelist()
def get_wishlist(page=1, page_size=20):
	"""Get user's wishlist"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		page = cint(page) or 1
		page_size = cint(page_size) or 20
		
		from dmsoko.user_management.doctype.wishlist.wishlist import Wishlist
		
		# Get wishlist items
		wishlist_items = Wishlist.get_user_wishlist(
			frappe.session.user,
			limit=page_size,
			offset=(page - 1) * page_size
		)
		
		# Get total count
		total = Wishlist.get_wishlist_count(frappe.session.user)
		
		return {
			"success": True,
			"data": {
				"wishlist": wishlist_items,
				"total": total,
				"page": page,
				"page_size": page_size,
				"total_pages": (total + page_size - 1) // page_size
			}
		}
		
	except Exception as e:
		frappe.log_error(f"Error in get_wishlist: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist()
def add_to_wishlist(listing_id, notes=None):
	"""Add listing to wishlist"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		# Check if listing exists and is active
		listing = frappe.get_doc("Listing", listing_id)
		if listing.status not in ["Active", "Draft"]:
			return {"success": False, "error": "Cannot add inactive listing to wishlist"}
		
		# Can't add own listing to wishlist
		if listing.created_by_user == frappe.session.user:
			return {"success": False, "error": "Cannot add your own listing to wishlist"}
		
		from dmsoko.user_management.doctype.wishlist.wishlist import Wishlist
		result = Wishlist.add_to_wishlist(frappe.session.user, listing_id, notes)
		
		return result
		
	except frappe.DoesNotExistError:
		return {"success": False, "error": "Listing not found"}
	except Exception as e:
		frappe.log_error(f"Error in add_to_wishlist: {str(e)}")
		return {"success": False, "error": "Failed to add to wishlist"}


@frappe.whitelist()
def remove_from_wishlist(listing_id):
	"""Remove listing from wishlist"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		from dmsoko.user_management.doctype.wishlist.wishlist import Wishlist
		result = Wishlist.remove_from_wishlist(frappe.session.user, listing_id)
		
		return result
		
	except Exception as e:
		frappe.log_error(f"Error in remove_from_wishlist: {str(e)}")
		return {"success": False, "error": "Failed to remove from wishlist"}


@frappe.whitelist()
def check_wishlist_status(listing_id):
	"""Check if listing is in user's wishlist"""
	try:
		if frappe.session.user == "Guest":
			return {"success": True, "data": {"in_wishlist": False}}
		
		from dmsoko.user_management.doctype.wishlist.wishlist import Wishlist
		in_wishlist = Wishlist.is_in_wishlist(frappe.session.user, listing_id)
		
		return {"success": True, "data": {"in_wishlist": in_wishlist}}
		
	except Exception as e:
		frappe.log_error(f"Error in check_wishlist_status: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist()
def update_wishlist_notes(listing_id, notes):
	"""Update notes for wishlist item"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		# Get wishlist item
		wishlist_item = frappe.db.exists("Wishlist", {
			"user": frappe.session.user,
			"listing": listing_id
		})
		
		if not wishlist_item:
			return {"success": False, "error": "Listing not in wishlist"}
		
		# Update notes
		frappe.db.set_value("Wishlist", wishlist_item, "notes", notes)
		frappe.db.commit()
		
		return {"success": True, "message": "Notes updated successfully"}
		
	except Exception as e:
		frappe.log_error(f"Error in update_wishlist_notes: {str(e)}")
		return {"success": False, "error": "Failed to update notes"}


@frappe.whitelist()
def get_wishlist_count():
	"""Get total wishlist count for current user"""
	try:
		if frappe.session.user == "Guest":
			return {"success": True, "data": {"count": 0}}
		
		from dmsoko.user_management.doctype.wishlist.wishlist import Wishlist
		count = Wishlist.get_wishlist_count(frappe.session.user)
		
		return {"success": True, "data": {"count": count}}
		
	except Exception as e:
		frappe.log_error(f"Error in get_wishlist_count: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist()
def clear_wishlist():
	"""Clear all items from user's wishlist"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		# Get all wishlist items for user
		wishlist_items = frappe.get_all("Wishlist", filters={"user": frappe.session.user}, pluck="name")
		
		# Delete all items
		for item_id in wishlist_items:
			frappe.delete_doc("Wishlist", item_id)
		
		return {"success": True, "message": f"Cleared {len(wishlist_items)} items from wishlist"}
		
	except Exception as e:
		frappe.log_error(f"Error in clear_wishlist: {str(e)}")
		return {"success": False, "error": "Failed to clear wishlist"}
