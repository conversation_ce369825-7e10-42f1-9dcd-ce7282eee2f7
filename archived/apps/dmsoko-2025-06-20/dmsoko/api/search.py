# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import cint, flt
import json


@frappe.whitelist(allow_guest=True)
def search_listings(query="", filters=None, page=1, page_size=20, sort_by="creation", sort_order="desc"):
	"""Advanced search for listings"""
	try:
		# Parse filters if string
		if isinstance(filters, str):
			filters = json.loads(filters)
		
		if not filters:
			filters = {}
		
		# Convert page and page_size to int
		page = cint(page) or 1
		page_size = cint(page_size) or 20
		
		# Build search conditions
		conditions = ["l.status = 'Active'", "l.expires_on >= CURDATE()"]
		values = []
		
		# Text search
		if query:
			conditions.append("(l.title LIKE %s OR l.description LIKE %s OR l.location LIKE %s)")
			search_term = f"%{query}%"
			values.extend([search_term, search_term, search_term])
		
		# Category filter
		if filters.get("category"):
			# Include subcategories
			category_ids = get_category_and_children(filters["category"])
			if category_ids:
				placeholders = ",".join(["%s"] * len(category_ids))
				conditions.append(f"l.category IN ({placeholders})")
				values.extend(category_ids)
		
		# Location filter
		if filters.get("location"):
			conditions.append("l.location LIKE %s")
			values.append(f"%{filters['location']}%")
		
		# Price range filter
		if filters.get("min_price"):
			conditions.append("l.price >= %s")
			values.append(flt(filters["min_price"]))
		
		if filters.get("max_price"):
			conditions.append("l.price <= %s")
			values.append(flt(filters["max_price"]))
		
		# Listing type filter
		if filters.get("listing_type"):
			conditions.append("l.listing_type = %s")
			values.append(filters["listing_type"])
		
		# Condition filter
		if filters.get("condition"):
			conditions.append("l.condition = %s")
			values.append(filters["condition"])
		
		# Featured filter
		if filters.get("featured_only"):
			conditions.append("l.featured = 1")
		
		# Date range filter
		if filters.get("date_from"):
			conditions.append("l.creation >= %s")
			values.append(filters["date_from"])
		
		if filters.get("date_to"):
			conditions.append("l.creation <= %s")
			values.append(filters["date_to"])
		
		# Build WHERE clause
		where_clause = " AND ".join(conditions)
		
		# Build ORDER BY clause
		valid_sort_fields = ["creation", "price", "title", "views_count", "modified"]
		if sort_by not in valid_sort_fields:
			sort_by = "creation"
		
		if sort_order.lower() not in ["asc", "desc"]:
			sort_order = "desc"
		
		order_clause = f"l.{sort_by} {sort_order}"
		
		# Get total count
		count_query = f"""
			SELECT COUNT(*)
			FROM `tabListing` l
			WHERE {where_clause}
		"""
		total = frappe.db.sql(count_query, values)[0][0]
		
		# Get listings
		offset = (page - 1) * page_size
		search_query = f"""
			SELECT 
				l.name,
				l.title,
				l.description,
				l.price,
				l.currency,
				l.location,
				l.category,
				l.listing_type,
				l.condition,
				l.creation,
				l.views_count,
				l.featured,
				l.created_by_user,
				c.category_name,
				u.full_name as user_name,
				(SELECT li.image FROM `tabListing Image` li 
				 WHERE li.parent = l.name AND li.is_primary = 1 
				 LIMIT 1) as primary_image
			FROM `tabListing` l
			LEFT JOIN `tabCategory` c ON c.name = l.category
			LEFT JOIN `tabUser` u ON u.name = l.created_by_user
			WHERE {where_clause}
			ORDER BY {order_clause}
			LIMIT %s OFFSET %s
		"""
		
		listings = frappe.db.sql(search_query, values + [page_size, offset], as_dict=True)
		
		return {
			"success": True,
			"data": {
				"listings": listings,
				"total": total,
				"page": page,
				"page_size": page_size,
				"total_pages": (total + page_size - 1) // page_size,
				"query": query,
				"filters": filters
			}
		}
		
	except Exception as e:
		frappe.log_error(f"Error in search_listings: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist(allow_guest=True)
def get_search_suggestions(query, limit=10):
	"""Get search suggestions based on query"""
	try:
		if not query or len(query) < 2:
			return {"success": True, "data": []}
		
		limit = cint(limit) or 10
		
		# Get title suggestions
		title_suggestions = frappe.db.sql("""
			SELECT DISTINCT title
			FROM `tabListing`
			WHERE title LIKE %s
			AND status = 'Active'
			AND expires_on >= CURDATE()
			ORDER BY views_count DESC
			LIMIT %s
		""", (f"%{query}%", limit // 2), as_dict=True)
		
		# Get location suggestions
		location_suggestions = frappe.db.sql("""
			SELECT DISTINCT location
			FROM `tabListing`
			WHERE location LIKE %s
			AND status = 'Active'
			AND expires_on >= CURDATE()
			ORDER BY COUNT(*) DESC
			LIMIT %s
		""", (f"%{query}%", limit // 2), as_dict=True)
		
		suggestions = []
		
		# Add title suggestions
		for item in title_suggestions:
			suggestions.append({
				"text": item.title,
				"type": "title"
			})
		
		# Add location suggestions
		for item in location_suggestions:
			suggestions.append({
				"text": item.location,
				"type": "location"
			})
		
		return {"success": True, "data": suggestions[:limit]}
		
	except Exception as e:
		frappe.log_error(f"Error in get_search_suggestions: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist(allow_guest=True)
def get_search_filters():
	"""Get available search filters"""
	try:
		# Get categories
		categories = frappe.get_all(
			"Category",
			filters={"is_active": 1},
			fields=["name", "category_name", "parent_category"],
			order_by="sort_order asc, category_name asc"
		)
		
		# Get locations (top 20 by listing count)
		locations = frappe.db.sql("""
			SELECT location, COUNT(*) as count
			FROM `tabListing`
			WHERE status = 'Active'
			AND expires_on >= CURDATE()
			AND location IS NOT NULL
			AND location != ''
			GROUP BY location
			ORDER BY count DESC
			LIMIT 20
		""", as_dict=True)
		
		# Get price ranges
		price_stats = frappe.db.sql("""
			SELECT 
				MIN(price) as min_price,
				MAX(price) as max_price,
				AVG(price) as avg_price
			FROM `tabListing`
			WHERE status = 'Active'
			AND expires_on >= CURDATE()
			AND price > 0
		""", as_dict=True)[0]
		
		# Get listing types
		listing_types = frappe.db.sql("""
			SELECT listing_type, COUNT(*) as count
			FROM `tabListing`
			WHERE status = 'Active'
			AND expires_on >= CURDATE()
			GROUP BY listing_type
			ORDER BY count DESC
		""", as_dict=True)
		
		# Get conditions
		conditions = frappe.db.sql("""
			SELECT condition, COUNT(*) as count
			FROM `tabListing`
			WHERE status = 'Active'
			AND expires_on >= CURDATE()
			GROUP BY condition
			ORDER BY count DESC
		""", as_dict=True)
		
		return {
			"success": True,
			"data": {
				"categories": categories,
				"locations": locations,
				"price_stats": price_stats,
				"listing_types": listing_types,
				"conditions": conditions
			}
		}
		
	except Exception as e:
		frappe.log_error(f"Error in get_search_filters: {str(e)}")
		return {"success": False, "error": str(e)}


def get_category_and_children(category_id):
	"""Get category and all its children IDs"""
	try:
		category_ids = [category_id]
		
		# Get all children recursively
		children = frappe.db.sql("""
			WITH RECURSIVE category_tree AS (
				SELECT name, parent_category
				FROM `tabCategory`
				WHERE parent_category = %s
				AND is_active = 1
				
				UNION ALL
				
				SELECT c.name, c.parent_category
				FROM `tabCategory` c
				INNER JOIN category_tree ct ON c.parent_category = ct.name
				WHERE c.is_active = 1
			)
			SELECT name FROM category_tree
		""", (category_id,), as_dict=True)
		
		category_ids.extend([child.name for child in children])
		
		return category_ids
		
	except Exception:
		# Fallback to just the main category if recursive query fails
		return [category_id]
