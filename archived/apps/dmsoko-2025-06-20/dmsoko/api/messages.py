# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import cint


@frappe.whitelist()
def get_conversations(page=1, page_size=20):
	"""Get user's conversations"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		page = cint(page) or 1
		page_size = cint(page_size) or 20
		
		from dmsoko.messaging.doctype.conversation.conversation import Conversation
		conversations = Conversation.get_user_conversations(
			frappe.session.user, 
			limit=page_size, 
			offset=(page - 1) * page_size
		)
		
		# Get total count
		total = frappe.db.count("Conversation", {
			"$or": [
				{"buyer": frappe.session.user},
				{"seller": frappe.session.user}
			],
			"is_active": 1
		})
		
		return {
			"success": True,
			"data": {
				"conversations": conversations,
				"total": total,
				"page": page,
				"page_size": page_size,
				"total_pages": (total + page_size - 1) // page_size
			}
		}
		
	except Exception as e:
		frappe.log_error(f"Error in get_conversations: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist()
def get_conversation(conversation_id):
	"""Get conversation details with messages"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		# Get conversation
		conversation = frappe.get_doc("Conversation", conversation_id)
		
		# Check permissions
		if not conversation.can_participate():
			return {"success": False, "error": "Permission denied"}
		
		# Get conversation data
		conversation_data = conversation.as_dict()
		
		# Get messages
		messages = conversation.get_messages(limit=100)
		conversation_data["messages"] = messages
		
		# Get listing info
		listing_info = conversation.get_listing_info()
		conversation_data["listing_info"] = listing_info
		
		# Get participant info
		buyer_info = frappe.get_value("User", conversation.buyer, ["full_name", "user_image"], as_dict=True)
		seller_info = frappe.get_value("User", conversation.seller, ["full_name", "user_image"], as_dict=True)
		
		conversation_data["buyer_info"] = buyer_info
		conversation_data["seller_info"] = seller_info
		
		# Mark messages as read
		conversation.mark_as_read(frappe.session.user)
		
		return {"success": True, "data": conversation_data}
		
	except frappe.DoesNotExistError:
		return {"success": False, "error": "Conversation not found"}
	except Exception as e:
		frappe.log_error(f"Error in get_conversation: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist()
def send_message(conversation_id=None, listing_id=None, message_text="", message_type="text", attachment=None):
	"""Send a message"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		if not message_text.strip():
			return {"success": False, "error": "Message cannot be empty"}
		
		conversation = None
		
		if conversation_id:
			# Use existing conversation
			conversation = frappe.get_doc("Conversation", conversation_id)
			
			# Check permissions
			if not conversation.can_participate():
				return {"success": False, "error": "Permission denied"}
		
		elif listing_id:
			# Create new conversation or get existing one
			listing = frappe.get_doc("Listing", listing_id)
			
			# Can't message own listing
			if listing.created_by_user == frappe.session.user:
				return {"success": False, "error": "Cannot message your own listing"}
			
			from dmsoko.messaging.doctype.conversation.conversation import Conversation
			conversation = Conversation.get_or_create_conversation(
				listing_id, 
				frappe.session.user,  # buyer
				listing.created_by_user  # seller
			)
		
		else:
			return {"success": False, "error": "Either conversation_id or listing_id is required"}
		
		# Send message
		message = conversation.send_message(
			sender=frappe.session.user,
			message_text=message_text,
			message_type=message_type,
			attachment=attachment
		)
		
		return {
			"success": True,
			"message": "Message sent successfully",
			"data": {
				"message_id": message.name,
				"conversation_id": conversation.name
			}
		}
		
	except frappe.DoesNotExistError:
		return {"success": False, "error": "Conversation or listing not found"}
	except Exception as e:
		frappe.log_error(f"Error in send_message: {str(e)}")
		return {"success": False, "error": "Failed to send message"}


@frappe.whitelist()
def mark_conversation_as_read(conversation_id):
	"""Mark all messages in conversation as read"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		# Get conversation
		conversation = frappe.get_doc("Conversation", conversation_id)
		
		# Check permissions
		if not conversation.can_participate():
			return {"success": False, "error": "Permission denied"}
		
		# Mark as read
		conversation.mark_as_read(frappe.session.user)
		
		return {"success": True, "message": "Messages marked as read"}
		
	except frappe.DoesNotExistError:
		return {"success": False, "error": "Conversation not found"}
	except Exception as e:
		frappe.log_error(f"Error in mark_conversation_as_read: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist()
def get_unread_count():
	"""Get unread message count for current user"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		from dmsoko.messaging.doctype.message.message import Message
		unread_count = Message.get_unread_count(frappe.session.user)
		
		return {"success": True, "data": {"unread_count": unread_count}}
		
	except Exception as e:
		frappe.log_error(f"Error in get_unread_count: {str(e)}")
		return {"success": False, "error": str(e)}


@frappe.whitelist()
def delete_message(message_id):
	"""Delete a message"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		# Get message
		message = frappe.get_doc("Message", message_id)
		
		# Check permissions
		if not message.can_delete():
			return {"success": False, "error": "Permission denied"}
		
		message.delete()
		
		return {"success": True, "message": "Message deleted successfully"}
		
	except frappe.DoesNotExistError:
		return {"success": False, "error": "Message not found"}
	except Exception as e:
		frappe.log_error(f"Error in delete_message: {str(e)}")
		return {"success": False, "error": "Failed to delete message"}


@frappe.whitelist()
def start_conversation(listing_id, initial_message):
	"""Start a new conversation about a listing"""
	try:
		if frappe.session.user == "Guest":
			return {"success": False, "error": "Authentication required"}
		
		if not initial_message.strip():
			return {"success": False, "error": "Initial message cannot be empty"}
		
		# Get listing
		listing = frappe.get_doc("Listing", listing_id)
		
		# Can't message own listing
		if listing.created_by_user == frappe.session.user:
			return {"success": False, "error": "Cannot message your own listing"}
		
		# Check if listing is active
		if listing.status != "Active":
			return {"success": False, "error": "Cannot message about inactive listing"}
		
		# Create or get conversation
		from dmsoko.messaging.doctype.conversation.conversation import Conversation
		conversation = Conversation.get_or_create_conversation(
			listing_id,
			frappe.session.user,  # buyer
			listing.created_by_user  # seller
		)
		
		# Send initial message
		message = conversation.send_message(
			sender=frappe.session.user,
			message_text=initial_message
		)
		
		return {
			"success": True,
			"message": "Conversation started successfully",
			"data": {
				"conversation_id": conversation.name,
				"message_id": message.name
			}
		}
		
	except frappe.DoesNotExistError:
		return {"success": False, "error": "Listing not found"}
	except Exception as e:
		frappe.log_error(f"Error in start_conversation: {str(e)}")
		return {"success": False, "error": "Failed to start conversation"}
