# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import now, add_days, cstr, flt
from frappe import _


class Listing(Document):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from frappe.types import DF
		from dmsoko.listings.doctype.listing_image.listing_image import ListingImage

		address: DF.Text | None
		approved_by: DF.Link | None
		approved_on: DF.Datetime | None
		category: DF.Link
		condition: DF.Literal["New", "Used", "Refurbished"]
		contact_email: DF.Data | None
		contact_phone: DF.Phone | None
		contact_whatsapp: DF.Phone | None
		created_by_user: DF.Link | None
		currency: DF.Link | None
		description: DF.TextEditor
		expires_on: DF.Date | None
		featured: DF.Check
		images: DF.Table[ListingImage]
		latitude: DF.Float
		listing_type: DF.Literal["For Sale", "For Rent", "Service", "Wanted"]
		location: DF.Data
		longitude: DF.Float
		meta_description: DF.Text | None
		meta_keywords: DF.Text | None
		meta_title: DF.Data | None
		naming_series: DF.Select
		price: DF.Currency
		show_contact_info: DF.Check
		status: DF.Literal["Draft", "Active", "Sold", "Expired", "Rejected"]
		title: DF.Data
		views_count: DF.Int
	# end: auto-generated types

	def before_insert(self):
		"""Set default values before inserting"""
		self.created_by_user = frappe.session.user
		self.set_default_expiry()
		self.set_default_contact_info()

	def validate(self):
		"""Validate listing data"""
		self.validate_contact_info()
		self.validate_price()
		self.validate_expiry_date()
		self.set_meta_title()
		self.ensure_primary_image()

	def validate_contact_info(self):
		"""Ensure at least one contact method is provided"""
		if not self.contact_phone and not self.contact_email and not self.contact_whatsapp:
			frappe.throw(_("Please provide at least one contact method (phone, email, or WhatsApp)"))

	def validate_price(self):
		"""Validate price for certain listing types"""
		if self.listing_type in ["For Sale", "For Rent"] and not self.price:
			frappe.throw(_("Price is required for {0} listings").format(self.listing_type))

	def validate_expiry_date(self):
		"""Validate expiry date"""
		if self.expires_on and self.expires_on < frappe.utils.today():
			frappe.throw(_("Expiry date cannot be in the past"))

	def set_default_expiry(self):
		"""Set default expiry date (30 days from now)"""
		if not self.expires_on:
			self.expires_on = add_days(frappe.utils.today(), 30)

	def set_default_contact_info(self):
		"""Set default contact info from user profile"""
		if not self.contact_email:
			user = frappe.get_doc("User", frappe.session.user)
			self.contact_email = user.email

	def set_meta_title(self):
		"""Set meta title if not provided"""
		if not self.meta_title:
			self.meta_title = self.title

	def ensure_primary_image(self):
		"""Ensure there's a primary image if images exist"""
		if self.images:
			has_primary = any(img.is_primary for img in self.images)
			if not has_primary:
				self.images[0].is_primary = 1

	def on_update(self):
		"""Handle updates"""
		self.update_search_index()

	def update_search_index(self):
		"""Update search index for better search functionality"""
		# This can be enhanced with full-text search indexing
		pass

	def increment_views(self):
		"""Increment view count"""
		frappe.db.set_value("Listing", self.name, "views_count", self.views_count + 1)
		frappe.db.commit()

	def get_primary_image(self):
		"""Get the primary image URL"""
		for image in self.images:
			if image.is_primary:
				return image.image
		
		# Return first image if no primary is set
		if self.images:
			return self.images[0].image
		
		return None

	def get_formatted_price(self):
		"""Get formatted price with currency"""
		if self.price:
			currency_symbol = frappe.db.get_value("Currency", self.currency, "symbol") or self.currency
			return f"{currency_symbol} {frappe.utils.fmt_money(self.price)}"
		return _("Price on request")

	def get_time_ago(self):
		"""Get human-readable time since creation"""
		return frappe.utils.pretty_date(self.creation)

	def get_similar_listings(self, limit=5):
		"""Get similar listings based on category and location"""
		filters = {
			"name": ["!=", self.name],
			"category": self.category,
			"status": "Active",
			"expires_on": [">=", frappe.utils.today()]
		}
		
		# Add location filter if available
		if self.location:
			filters["location"] = ["like", f"%{self.location}%"]

		return frappe.get_all(
			"Listing",
			filters=filters,
			fields=["name", "title", "price", "currency", "location", "creation"],
			order_by="creation desc",
			limit=limit
		)

	def can_edit(self, user=None):
		"""Check if user can edit this listing"""
		if not user:
			user = frappe.session.user
		
		# Owner can always edit
		if self.created_by_user == user:
			return True
		
		# Admin and moderators can edit
		user_roles = frappe.get_roles(user)
		if "DMSoko Admin" in user_roles or "System Manager" in user_roles:
			return True
		
		return False

	def can_delete(self, user=None):
		"""Check if user can delete this listing"""
		if not user:
			user = frappe.session.user
		
		# Owner can delete if status is Draft
		if self.created_by_user == user and self.status == "Draft":
			return True
		
		# Admin can always delete
		user_roles = frappe.get_roles(user)
		if "DMSoko Admin" in user_roles or "System Manager" in user_roles:
			return True
		
		return False

	def approve(self, approved_by=None):
		"""Approve the listing"""
		if not approved_by:
			approved_by = frappe.session.user
		
		self.status = "Active"
		self.approved_by = approved_by
		self.approved_on = now()
		self.save()

	def reject(self, reason=None):
		"""Reject the listing"""
		self.status = "Rejected"
		self.save()
		
		# Send notification to owner
		if reason:
			self.send_rejection_notification(reason)

	def mark_as_sold(self):
		"""Mark listing as sold"""
		self.status = "Sold"
		self.save()

	def send_rejection_notification(self, reason):
		"""Send rejection notification to listing owner"""
		try:
			frappe.sendmail(
				recipients=[self.contact_email],
				subject=f"Your listing '{self.title}' has been rejected",
				message=f"""
				<p>Dear User,</p>
				<p>Your listing "<strong>{self.title}</strong>" has been rejected.</p>
				<p><strong>Reason:</strong> {reason}</p>
				<p>Please review our listing guidelines and resubmit if needed.</p>
				<p>Best regards,<br>DMSoko Team</p>
				"""
			)
		except Exception as e:
			frappe.log_error(f"Failed to send rejection notification: {str(e)}")

	@staticmethod
	def get_featured_listings(limit=6):
		"""Get featured listings"""
		return frappe.get_all(
			"Listing",
			filters={
				"status": "Active",
				"featured": 1,
				"expires_on": [">=", frappe.utils.today()]
			},
			fields=["name", "title", "price", "currency", "location", "creation"],
			order_by="creation desc",
			limit=limit
		)

	@staticmethod
	def get_recent_listings(limit=8):
		"""Get recent active listings"""
		return frappe.get_all(
			"Listing",
			filters={
				"status": "Active",
				"expires_on": [">=", frappe.utils.today()]
			},
			fields=["name", "title", "price", "currency", "location", "creation"],
			order_by="creation desc",
			limit=limit
		)

	@staticmethod
	def search_listings(query, filters=None, page=1, page_size=20):
		"""Search listings with filters"""
		if not filters:
			filters = {}
		
		# Base filters
		base_filters = {
			"status": "Active",
			"expires_on": [">=", frappe.utils.today()]
		}
		base_filters.update(filters)
		
		# Add search query
		if query:
			base_filters["title"] = ["like", f"%{query}%"]
		
		# Get total count
		total = frappe.db.count("Listing", base_filters)
		
		# Get listings
		listings = frappe.get_all(
			"Listing",
			filters=base_filters,
			fields=["name", "title", "price", "currency", "location", "creation", "category"],
			order_by="creation desc",
			start=(page - 1) * page_size,
			page_length=page_size
		)
		
		return {
			"listings": listings,
			"total": total,
			"page": page,
			"page_size": page_size,
			"total_pages": (total + page_size - 1) // page_size
		}
