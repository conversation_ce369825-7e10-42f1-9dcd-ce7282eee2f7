{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2024-06-19 12:00:00.000000", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["naming_series", "title", "description", "category", "column_break_5", "price", "currency", "listing_type", "condition", "section_break_10", "location", "address", "latitude", "longitude", "column_break_15", "contact_phone", "contact_email", "contact_whatsapp", "show_contact_info", "section_break_20", "images", "section_break_22", "status", "featured", "expires_on", "column_break_26", "views_count", "created_by_user", "approved_by", "approved_on", "section_break_31", "meta_title", "meta_description", "meta_keywords"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "LST-.YYYY.-.MM.-.#####", "reqd": 1}, {"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "reqd": 1}, {"fieldname": "category", "fieldtype": "Link", "in_list_view": 1, "label": "Category", "options": "Category", "reqd": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Price", "precision": "2"}, {"default": "USD", "fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"default": "For Sale", "fieldname": "listing_type", "fieldtype": "Select", "in_list_view": 1, "label": "Listing Type", "options": "For Sale\nFor Rent\nService\nWanted", "reqd": 1}, {"default": "New", "fieldname": "condition", "fieldtype": "Select", "label": "Condition", "options": "New\nUsed\nRefurbished"}, {"fieldname": "section_break_10", "fieldtype": "Section Break", "label": "Location & Contact"}, {"fieldname": "location", "fieldtype": "Data", "in_list_view": 1, "label": "Location", "reqd": 1}, {"fieldname": "address", "fieldtype": "Text", "label": "Full Address"}, {"fieldname": "latitude", "fieldtype": "Float", "label": "Latitude", "precision": "8"}, {"fieldname": "longitude", "fieldtype": "Float", "label": "Longitude", "precision": "8"}, {"fieldname": "column_break_15", "fieldtype": "Column Break"}, {"fieldname": "contact_phone", "fieldtype": "Phone", "label": "Contact Phone"}, {"fieldname": "contact_email", "fieldtype": "Data", "label": "Contact Email"}, {"fieldname": "contact_whatsapp", "fieldtype": "Phone", "label": "WhatsApp Number"}, {"default": "1", "fieldname": "show_contact_info", "fieldtype": "Check", "label": "Show Contact Info"}, {"fieldname": "section_break_20", "fieldtype": "Section Break", "label": "Images"}, {"fieldname": "images", "fieldtype": "Table", "label": "Images", "options": "Listing Image"}, {"fieldname": "section_break_22", "fieldtype": "Section Break", "label": "Status & Settings"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Draft\nActive\nSold\nExpired\nRejected", "reqd": 1}, {"default": "0", "fieldname": "featured", "fieldtype": "Check", "label": "Featured Listing"}, {"fieldname": "expires_on", "fieldtype": "Date", "label": "Expires On"}, {"fieldname": "column_break_26", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "views_count", "fieldtype": "Int", "label": "Views Count", "read_only": 1}, {"fieldname": "created_by_user", "fieldtype": "Link", "label": "Created By", "options": "User", "read_only": 1}, {"fieldname": "approved_by", "fieldtype": "Link", "label": "Approved By", "options": "User", "read_only": 1}, {"fieldname": "approved_on", "fieldtype": "Datetime", "label": "Approved On", "read_only": 1}, {"fieldname": "section_break_31", "fieldtype": "Section Break", "label": "SEO Settings"}, {"fieldname": "meta_title", "fieldtype": "Data", "label": "Meta Title"}, {"fieldname": "meta_description", "fieldtype": "Text", "label": "Meta Description"}, {"fieldname": "meta_keywords", "fieldtype": "Text", "label": "Meta Keywords"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-06-19 12:00:00.000000", "modified_by": "Administrator", "module": "Listings", "name": "Listing", "app": "dms<PERSON>", "naming_rule": "By Naming Series", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON><PERSON><PERSON>", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "DMSoko User", "share": 1, "write": 1, "if_owner": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1, "track_views": 1}