# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class ListingImage(Document):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from frappe.types import DF

		caption: DF.Data | None
		image: DF.AttachImage
		is_primary: DF.Check
		parent: DF.Data
		parentfield: DF.Data
		parenttype: DF.Data
	# end: auto-generated types

	def validate(self):
		"""Validate listing image"""
		self.ensure_single_primary_image()

	def ensure_single_primary_image(self):
		"""Ensure only one primary image per listing"""
		if self.is_primary and self.parent:
			# Get the parent listing document
			listing = frappe.get_doc("Listing", self.parent)
			
			# Check if there's already a primary image
			for img in listing.images:
				if img.name != self.name and img.is_primary:
					img.is_primary = 0
			
			# If this is the first image, make it primary
			if len(listing.images) == 1:
				self.is_primary = 1
