import frappe
from frappe import _

def get_context(context):
    """
    Get context for the DMSoko web page
    """
    context.no_cache = 1
    
    # Set page title based on route
    route = frappe.local.request.path
    if route.endswith('/listings'):
        context.title = _("Browse Listings - DMSoko")
    elif route.endswith('/post'):
        context.title = _("Post New Listing - DMSoko")
    elif route.endswith('/login'):
        context.title = _("Login - DMSoko")
    elif route.endswith('/register'):
        context.title = _("Sign Up - DMSoko")
    elif '/listing/' in route:
        # Try to get listing title for SEO
        listing_id = route.split('/listing/')[-1]
        try:
            listing = frappe.get_doc("Listing", listing_id)
            context.title = f"{listing.title} - DMSoko"
        except:
            context.title = _("Listing Details - DMSoko")
    else:
        context.title = _("DMSoko - Your Marketplace")
    
    # Add meta description
    context.meta_description = _("DMSoko - Buy and sell anything in your local area. Post free classified ads for cars, jobs, real estate, and more.")
    
    # Add structured data for SEO
    context.structured_data = {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "DMSoko",
        "url": frappe.utils.get_url() + "/dmsoko",
        "description": context.meta_description,
        "potentialAction": {
            "@type": "SearchAction",
            "target": frappe.utils.get_url() + "/dmsoko/listings?search={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    
    # Check if user is logged in
    context.is_logged_in = frappe.session.user != "Guest"
    
    # Get user info if logged in
    if context.is_logged_in:
        user = frappe.get_doc("User", frappe.session.user)
        context.user_info = {
            "name": user.name,
            "full_name": user.full_name,
            "email": user.email,
            "user_image": user.user_image
        }
    
    return context
