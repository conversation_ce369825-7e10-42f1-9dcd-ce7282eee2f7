# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import now
from frappe import _


class Wishlist(Document):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from frappe.types import DF

		added_date: DF.Datetime
		listing: DF.Link
		notes: DF.Text | None
		user: DF.Link
	# end: auto-generated types

	def before_insert(self):
		"""Set added_date before inserting"""
		self.added_date = now()

	def validate(self):
		"""Validate wishlist entry"""
		self.check_duplicate_entry()
		self.validate_listing_status()

	def check_duplicate_entry(self):
		"""Check for duplicate wishlist entry"""
		existing = frappe.db.exists("Wishlist", {
			"user": self.user,
			"listing": self.listing,
			"name": ["!=", self.name] if not self.is_new() else ["!=", ""]
		})
		
		if existing:
			frappe.throw(_("This listing is already in your wishlist"))

	def validate_listing_status(self):
		"""Ensure listing is active"""
		listing_status = frappe.db.get_value("Listing", self.listing, "status")
		if listing_status not in ["Active", "Draft"]:
			frappe.throw(_("Cannot add inactive listing to wishlist"))

	def can_access(self, user=None):
		"""Check if user can access this wishlist entry"""
		if not user:
			user = frappe.session.user
		
		# Owner can access
		if self.user == user:
			return True
		
		# Admin can access
		user_roles = frappe.get_roles(user)
		if "DMSoko Admin" in user_roles or "System Manager" in user_roles:
			return True
		
		return False

	@staticmethod
	def add_to_wishlist(user, listing_id, notes=None):
		"""Add listing to user's wishlist"""
		# Check if already exists
		existing = frappe.db.exists("Wishlist", {
			"user": user,
			"listing": listing_id
		})
		
		if existing:
			return {"success": False, "message": _("Listing already in wishlist")}
		
		# Create wishlist entry
		wishlist = frappe.get_doc({
			"doctype": "Wishlist",
			"user": user,
			"listing": listing_id,
			"notes": notes
		})
		wishlist.insert()
		
		return {"success": True, "message": _("Added to wishlist"), "wishlist_id": wishlist.name}

	@staticmethod
	def remove_from_wishlist(user, listing_id):
		"""Remove listing from user's wishlist"""
		existing = frappe.db.exists("Wishlist", {
			"user": user,
			"listing": listing_id
		})
		
		if not existing:
			return {"success": False, "message": _("Listing not in wishlist")}
		
		frappe.delete_doc("Wishlist", existing)
		
		return {"success": True, "message": _("Removed from wishlist")}

	@staticmethod
	def is_in_wishlist(user, listing_id):
		"""Check if listing is in user's wishlist"""
		return frappe.db.exists("Wishlist", {
			"user": user,
			"listing": listing_id
		}) is not None

	@staticmethod
	def get_user_wishlist(user, limit=20, offset=0):
		"""Get user's wishlist with listing details"""
		wishlist_items = frappe.db.sql("""
			SELECT 
				w.name as wishlist_id,
				w.listing,
				w.added_date,
				w.notes,
				l.title,
				l.price,
				l.currency,
				l.location,
				l.status,
				l.creation,
				(SELECT li.image FROM `tabListing Image` li 
				 WHERE li.parent = l.name AND li.is_primary = 1 
				 LIMIT 1) as primary_image
			FROM `tabWishlist` w
			LEFT JOIN `tabListing` l ON l.name = w.listing
			WHERE w.user = %(user)s
			ORDER BY w.added_date DESC
			LIMIT %(limit)s OFFSET %(offset)s
		""", {
			"user": user,
			"limit": limit,
			"offset": offset
		}, as_dict=True)
		
		return wishlist_items

	@staticmethod
	def get_wishlist_count(user):
		"""Get total wishlist count for user"""
		return frappe.db.count("Wishlist", {"user": user})

	@staticmethod
	def cleanup_expired_listings():
		"""Remove wishlist entries for expired/inactive listings"""
		expired_wishlists = frappe.db.sql("""
			SELECT w.name
			FROM `tabWishlist` w
			LEFT JOIN `tabListing` l ON l.name = w.listing
			WHERE l.status IN ('Sold', 'Expired', 'Rejected')
			OR l.expires_on < CURDATE()
		""", as_dict=True)
		
		for item in expired_wishlists:
			frappe.delete_doc("Wishlist", item.name)
		
		return len(expired_wishlists)
