# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import now, today
from frappe import _


class UserProfile(Document):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from frappe.types import DF

		active_listings: DF.Int
		address: DF.Text | None
		bio: DF.Text | None
		cover_image: DF.AttachImage | None
		email_notifications: DF.Check
		facebook_url: DF.Data | None
		instagram_url: DF.Data | None
		join_date: DF.Date
		last_active: DF.Datetime
		linkedin_url: DF.Data | None
		location: DF.Data | None
		marketing_emails: DF.Check
		phone_number: DF.Phone | None
		profile_image: DF.AttachImage | None
		rating: DF.Float
		sms_notifications: DF.Check
		sold_listings: DF.Int
		total_listings: DF.Int
		total_ratings: DF.Int
		twitter_url: DF.Data | None
		user: DF.Link
		verification_status: DF.Literal["Unverified", "Pending", "Verified", "Rejected"]
		whatsapp_number: DF.Phone | None
	# end: auto-generated types

	def before_insert(self):
		"""Set default values before inserting"""
		self.join_date = today()
		self.last_active = now()
		self.update_listing_stats()

	def validate(self):
		"""Validate user profile data"""
		self.validate_social_urls()
		self.update_listing_stats()

	def validate_social_urls(self):
		"""Validate social media URLs"""
		social_fields = {
			'facebook_url': 'facebook.com',
			'twitter_url': 'twitter.com',
			'instagram_url': 'instagram.com',
			'linkedin_url': 'linkedin.com'
		}
		
		for field, domain in social_fields.items():
			url = getattr(self, field)
			if url and domain not in url.lower():
				frappe.throw(_(f"Invalid {field.replace('_url', '').title()} URL"))

	def update_listing_stats(self):
		"""Update listing statistics"""
		# Get listing counts
		stats = frappe.db.sql("""
			SELECT 
				COUNT(*) as total,
				SUM(CASE WHEN status = 'Active' THEN 1 ELSE 0 END) as active,
				SUM(CASE WHEN status = 'Sold' THEN 1 ELSE 0 END) as sold
			FROM `tabListing`
			WHERE created_by_user = %s
		""", (self.user,), as_dict=True)[0]
		
		self.total_listings = stats.total or 0
		self.active_listings = stats.active or 0
		self.sold_listings = stats.sold or 0

	def update_last_active(self):
		"""Update last active timestamp"""
		frappe.db.set_value("User Profile", self.name, "last_active", now())

	def get_user_info(self):
		"""Get combined user and profile information"""
		user = frappe.get_doc("User", self.user)
		
		return {
			"name": user.name,
			"email": user.email,
			"full_name": user.full_name,
			"first_name": user.first_name,
			"last_name": user.last_name,
			"user_image": user.user_image or self.profile_image,
			"phone_number": self.phone_number,
			"whatsapp_number": self.whatsapp_number,
			"location": self.location,
			"address": self.address,
			"bio": self.bio,
			"verification_status": self.verification_status,
			"rating": self.rating,
			"total_ratings": self.total_ratings,
			"total_listings": self.total_listings,
			"active_listings": self.active_listings,
			"sold_listings": self.sold_listings,
			"join_date": self.join_date,
			"last_active": self.last_active
		}

	def can_edit(self, user=None):
		"""Check if user can edit this profile"""
		if not user:
			user = frappe.session.user
		
		# Owner can edit
		if self.user == user:
			return True
		
		# Admin can edit
		user_roles = frappe.get_roles(user)
		if "DMSoko Admin" in user_roles or "System Manager" in user_roles:
			return True
		
		return False

	def verify_user(self, verified_by=None):
		"""Verify user profile"""
		if not verified_by:
			verified_by = frappe.session.user
		
		self.verification_status = "Verified"
		self.save()
		
		# Send verification notification
		self.send_verification_notification()

	def reject_verification(self, reason=None):
		"""Reject user verification"""
		self.verification_status = "Rejected"
		self.save()
		
		# Send rejection notification
		if reason:
			self.send_rejection_notification(reason)

	def send_verification_notification(self):
		"""Send verification success notification"""
		try:
			user = frappe.get_doc("User", self.user)
			frappe.sendmail(
				recipients=[user.email],
				subject="Your DMSoko account has been verified!",
				message=f"""
				<p>Congratulations! Your DMSoko account has been verified.</p>
				<p>You can now enjoy all the benefits of a verified user, including:</p>
				<ul>
					<li>Increased trust from other users</li>
					<li>Priority in search results</li>
					<li>Access to premium features</li>
				</ul>
				<p>Thank you for being part of the DMSoko community!</p>
				"""
			)
		except Exception as e:
			frappe.log_error(f"Failed to send verification notification: {str(e)}")

	def send_rejection_notification(self, reason):
		"""Send verification rejection notification"""
		try:
			user = frappe.get_doc("User", self.user)
			frappe.sendmail(
				recipients=[user.email],
				subject="DMSoko account verification update",
				message=f"""
				<p>We were unable to verify your DMSoko account at this time.</p>
				<p><strong>Reason:</strong> {reason}</p>
				<p>Please review our verification guidelines and resubmit your verification request.</p>
				<p>If you have any questions, please contact our support team.</p>
				"""
			)
		except Exception as e:
			frappe.log_error(f"Failed to send rejection notification: {str(e)}")

	@staticmethod
	def get_or_create_profile(user):
		"""Get existing profile or create new one"""
		existing = frappe.db.exists("User Profile", user)
		
		if existing:
			return frappe.get_doc("User Profile", existing)
		
		# Create new profile
		profile = frappe.get_doc({
			"doctype": "User Profile",
			"user": user
		})
		profile.insert()
		
		return profile

	@staticmethod
	def get_top_sellers(limit=10):
		"""Get top sellers based on sold listings"""
		return frappe.db.sql("""
			SELECT 
				up.user,
				u.full_name,
				up.profile_image,
				up.location,
				up.rating,
				up.total_ratings,
				up.sold_listings,
				up.verification_status
			FROM `tabUser Profile` up
			LEFT JOIN `tabUser` u ON u.name = up.user
			WHERE up.sold_listings > 0
			ORDER BY up.sold_listings DESC, up.rating DESC
			LIMIT %s
		""", (limit,), as_dict=True)

	@staticmethod
	def search_users(query, filters=None, limit=20):
		"""Search users by name or location"""
		if not filters:
			filters = {}
		
		conditions = []
		values = []
		
		if query:
			conditions.append("(u.full_name LIKE %s OR up.location LIKE %s)")
			values.extend([f"%{query}%", f"%{query}%"])
		
		if filters.get("location"):
			conditions.append("up.location LIKE %s")
			values.append(f"%{filters['location']}%")
		
		if filters.get("verification_status"):
			conditions.append("up.verification_status = %s")
			values.append(filters["verification_status"])
		
		where_clause = " AND ".join(conditions) if conditions else "1=1"
		
		return frappe.db.sql(f"""
			SELECT 
				up.user,
				u.full_name,
				up.profile_image,
				up.location,
				up.rating,
				up.total_ratings,
				up.verification_status,
				up.total_listings,
				up.join_date
			FROM `tabUser Profile` up
			LEFT JOIN `tabUser` u ON u.name = up.user
			WHERE {where_clause}
			ORDER BY up.rating DESC, up.total_ratings DESC
			LIMIT %s
		""", values + [limit], as_dict=True)
