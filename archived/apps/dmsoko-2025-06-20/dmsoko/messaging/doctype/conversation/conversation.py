# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import now
from frappe import _


class Conversation(Document):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from frappe.types import DF

		buyer: DF.Link
		created_on: DF.Datetime
		is_active: DF.Check
		last_message: DF.Text | None
		last_message_by: DF.Link | None
		last_message_time: DF.Datetime | None
		listing: DF.Link
		seller: DF.Link
	# end: auto-generated types

	def before_insert(self):
		"""Set created_on timestamp"""
		self.created_on = now()

	def validate(self):
		"""Validate conversation data"""
		self.validate_participants()
		self.check_duplicate_conversation()

	def validate_participants(self):
		"""Ensure buyer and seller are different"""
		if self.buyer == self.seller:
			frappe.throw(_("Buyer and seller cannot be the same"))

	def check_duplicate_conversation(self):
		"""Check for existing conversation between same participants for same listing"""
		existing = frappe.db.exists("Conversation", {
			"listing": self.listing,
			"buyer": self.buyer,
			"seller": self.seller,
			"name": ["!=", self.name] if not self.is_new() else ["!=", ""]
		})
		
		if existing:
			frappe.throw(_("A conversation already exists between these participants for this listing"))

	def get_messages(self, limit=50, offset=0):
		"""Get messages in this conversation"""
		return frappe.get_all(
			"Message",
			filters={"conversation": self.name},
			fields=["name", "sender", "receiver", "message_text", "message_type", "attachment", "timestamp", "is_read"],
			order_by="timestamp asc",
			limit=limit,
			start=offset
		)

	def get_unread_count(self, user):
		"""Get unread message count for specific user"""
		return frappe.db.count("Message", {
			"conversation": self.name,
			"receiver": user,
			"is_read": 0
		})

	def mark_as_read(self, user):
		"""Mark all messages as read for specific user"""
		from dmsoko.messaging.doctype.message.message import Message
		Message.mark_conversation_as_read(self.name, user)

	def send_message(self, sender, message_text, message_type="text", attachment=None):
		"""Send a message in this conversation"""
		# Determine receiver
		receiver = self.seller if sender == self.buyer else self.buyer
		
		# Create message
		message = frappe.get_doc({
			"doctype": "Message",
			"conversation": self.name,
			"listing": self.listing,
			"sender": sender,
			"receiver": receiver,
			"message_text": message_text,
			"message_type": message_type,
			"attachment": attachment
		})
		message.insert()
		
		return message

	def can_participate(self, user=None):
		"""Check if user can participate in this conversation"""
		if not user:
			user = frappe.session.user
		
		# Participants can access
		if user in [self.buyer, self.seller]:
			return True
		
		# Admin can access
		user_roles = frappe.get_roles(user)
		if "DMSoko Admin" in user_roles or "System Manager" in user_roles:
			return True
		
		return False

	def get_other_participant(self, user):
		"""Get the other participant in the conversation"""
		if user == self.buyer:
			return self.seller
		elif user == self.seller:
			return self.buyer
		else:
			return None

	def get_listing_info(self):
		"""Get basic listing information"""
		return frappe.get_value(
			"Listing",
			self.listing,
			["title", "price", "currency", "status"],
			as_dict=True
		)

	@staticmethod
	def get_or_create_conversation(listing_id, buyer, seller):
		"""Get existing conversation or create new one"""
		# Check if conversation exists
		existing = frappe.db.exists("Conversation", {
			"listing": listing_id,
			"buyer": buyer,
			"seller": seller
		})
		
		if existing:
			return frappe.get_doc("Conversation", existing)
		
		# Create new conversation
		conversation = frappe.get_doc({
			"doctype": "Conversation",
			"listing": listing_id,
			"buyer": buyer,
			"seller": seller
		})
		conversation.insert()
		
		return conversation

	@staticmethod
	def get_user_conversations(user, limit=20, offset=0):
		"""Get all conversations for a user"""
		conversations = frappe.db.sql("""
			SELECT 
				c.name,
				c.listing,
				c.buyer,
				c.seller,
				c.last_message,
				c.last_message_time,
				c.last_message_by,
				l.title as listing_title,
				l.price as listing_price,
				l.currency as listing_currency,
				CASE 
					WHEN c.buyer = %(user)s THEN u_seller.full_name
					ELSE u_buyer.full_name
				END as other_participant_name,
				CASE 
					WHEN c.buyer = %(user)s THEN u_seller.user_image
					ELSE u_buyer.user_image
				END as other_participant_image,
				(SELECT COUNT(*) FROM `tabMessage` m 
				 WHERE m.conversation = c.name 
				 AND m.receiver = %(user)s 
				 AND m.is_read = 0) as unread_count
			FROM `tabConversation` c
			LEFT JOIN `tabListing` l ON l.name = c.listing
			LEFT JOIN `tabUser` u_buyer ON u_buyer.name = c.buyer
			LEFT JOIN `tabUser` u_seller ON u_seller.name = c.seller
			WHERE (c.buyer = %(user)s OR c.seller = %(user)s)
			AND c.is_active = 1
			ORDER BY c.last_message_time DESC
			LIMIT %(limit)s OFFSET %(offset)s
		""", {
			"user": user,
			"limit": limit,
			"offset": offset
		}, as_dict=True)
		
		return conversations

	@staticmethod
	def get_conversation_stats(user):
		"""Get conversation statistics for user"""
		total_conversations = frappe.db.count("Conversation", {
			"$or": [
				{"buyer": user},
				{"seller": user}
			],
			"is_active": 1
		})
		
		unread_messages = frappe.db.count("Message", {
			"receiver": user,
			"is_read": 0
		})
		
		return {
			"total_conversations": total_conversations,
			"unread_messages": unread_messages
		}
