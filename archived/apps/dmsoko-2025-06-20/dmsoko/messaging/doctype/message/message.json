{"actions": [], "autoname": "hash", "creation": "2024-06-19 12:00:00.000000", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["conversation", "listing", "sender", "receiver", "column_break_5", "message_text", "message_type", "attachment", "section_break_9", "is_read", "read_on", "timestamp"], "fields": [{"fieldname": "conversation", "fieldtype": "Link", "label": "Conversation", "options": "Conversation", "reqd": 1}, {"fieldname": "listing", "fieldtype": "Link", "label": "Listing", "options": "Listing", "reqd": 1}, {"fieldname": "sender", "fieldtype": "Link", "in_list_view": 1, "label": "Sender", "options": "User", "reqd": 1}, {"fieldname": "receiver", "fieldtype": "Link", "in_list_view": 1, "label": "Receiver", "options": "User", "reqd": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "message_text", "fieldtype": "Long Text", "in_list_view": 1, "label": "Message", "reqd": 1}, {"default": "text", "fieldname": "message_type", "fieldtype": "Select", "label": "Message Type", "options": "text\nimage\nfile", "reqd": 1}, {"depends_on": "eval:doc.message_type != 'text'", "fieldname": "attachment", "fieldtype": "Attach", "label": "Attachment"}, {"fieldname": "section_break_9", "fieldtype": "Section Break", "label": "Status"}, {"default": "0", "fieldname": "is_read", "fieldtype": "Check", "in_list_view": 1, "label": "<PERSON>"}, {"fieldname": "read_on", "fieldtype": "Datetime", "label": "Read On", "read_only": 1}, {"fieldname": "timestamp", "fieldtype": "Datetime", "in_list_view": 1, "label": "Timestamp", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-06-19 12:00:00.000000", "modified_by": "Administrator", "module": "Messaging", "name": "Message", "app": "dms<PERSON>", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON><PERSON><PERSON>", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "DMSoko User", "share": 1, "write": 1, "if_owner": 1}], "sort_field": "timestamp", "sort_order": "DESC", "states": [], "track_changes": 1}