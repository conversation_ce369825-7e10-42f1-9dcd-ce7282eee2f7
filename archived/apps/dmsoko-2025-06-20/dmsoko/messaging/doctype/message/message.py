# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import now
from frappe import _


class Message(Document):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from frappe.types import DF

		attachment: DF.Attach | None
		conversation: DF.Link
		is_read: DF.Check
		listing: DF.Link
		message_text: DF.LongText
		message_type: DF.Literal["text", "image", "file"]
		read_on: DF.Datetime | None
		receiver: DF.Link
		sender: DF.Link
		timestamp: DF.Datetime
	# end: auto-generated types

	def before_insert(self):
		"""Set timestamp before inserting"""
		self.timestamp = now()

	def validate(self):
		"""Validate message data"""
		self.validate_participants()
		self.validate_attachment()

	def validate_participants(self):
		"""Ensure sender and receiver are different"""
		if self.sender == self.receiver:
			frappe.throw(_("Sender and receiver cannot be the same"))

	def validate_attachment(self):
		"""Validate attachment for non-text messages"""
		if self.message_type != "text" and not self.attachment:
			frappe.throw(_("Attachment is required for {0} messages").format(self.message_type))

	def after_insert(self):
		"""Handle post-insertion tasks"""
		self.update_conversation()
		self.send_notification()

	def update_conversation(self):
		"""Update conversation with latest message info"""
		conversation = frappe.get_doc("Conversation", self.conversation)
		conversation.last_message = self.message_text[:100] + "..." if len(self.message_text) > 100 else self.message_text
		conversation.last_message_time = self.timestamp
		conversation.last_message_by = self.sender
		conversation.save(ignore_permissions=True)

	def send_notification(self):
		"""Send notification to receiver"""
		try:
			# Get receiver details
			receiver = frappe.get_doc("User", self.receiver)
			listing = frappe.get_doc("Listing", self.listing)
			
			# Send email notification
			frappe.sendmail(
				recipients=[receiver.email],
				subject=f"New message about '{listing.title}'",
				message=f"""
				<p>You have received a new message about your listing "<strong>{listing.title}</strong>".</p>
				<p><strong>From:</strong> {frappe.get_value("User", self.sender, "full_name")}</p>
				<p><strong>Message:</strong> {self.message_text}</p>
				<p><a href="{frappe.utils.get_url()}/dmsoko/messages?conversation={self.conversation}">View Conversation</a></p>
				"""
			)
		except Exception as e:
			frappe.log_error(f"Failed to send message notification: {str(e)}")

	def mark_as_read(self):
		"""Mark message as read"""
		if not self.is_read:
			self.is_read = 1
			self.read_on = now()
			self.save(ignore_permissions=True)

	def can_read(self, user=None):
		"""Check if user can read this message"""
		if not user:
			user = frappe.session.user
		
		# Participants can read
		if user in [self.sender, self.receiver]:
			return True
		
		# Admin can read
		user_roles = frappe.get_roles(user)
		if "DMSoko Admin" in user_roles or "System Manager" in user_roles:
			return True
		
		return False

	def can_delete(self, user=None):
		"""Check if user can delete this message"""
		if not user:
			user = frappe.session.user
		
		# Sender can delete their own messages
		if self.sender == user:
			return True
		
		# Admin can delete
		user_roles = frappe.get_roles(user)
		if "DMSoko Admin" in user_roles or "System Manager" in user_roles:
			return True
		
		return False

	def get_formatted_timestamp(self):
		"""Get formatted timestamp"""
		return frappe.utils.pretty_date(self.timestamp)

	@staticmethod
	def get_unread_count(user):
		"""Get unread message count for user"""
		return frappe.db.count("Message", {
			"receiver": user,
			"is_read": 0
		})

	@staticmethod
	def mark_conversation_as_read(conversation_id, user):
		"""Mark all messages in conversation as read for user"""
		messages = frappe.get_all(
			"Message",
			filters={
				"conversation": conversation_id,
				"receiver": user,
				"is_read": 0
			},
			pluck="name"
		)
		
		for message_id in messages:
			message = frappe.get_doc("Message", message_id)
			message.mark_as_read()
