---

  - hosts: localhost
    become: yes
    become_user: root
    tasks:
    - name: Create user
      user:
        name: '{{ frappe_user }}'
        generate_ssh_key: yes

    - name: Set home folder perms
      file:
        path: '{{ user_directory }}'
        mode: 'o+rx'
        owner: '{{ frappe_user }}'
        group: '{{ frappe_user }}'
        recurse: yes

    - name: Set /tmp/.bench folder perms
      file:
        path: '{{ repo_path }}'
        owner: '{{ frappe_user }}'
        group: '{{ frappe_user }}'
        recurse: yes

    - name: Change default shell to bash
      shell: "chsh {{ frappe_user }} -s $(which bash)"
...
