---

- debug:
    msg: "{{ mysql_root_password }}"

# create root .my.cnf config file
- name: Add .my.cnf
  template: src=my.cnf.j2 dest=/root/.my.cnf owner=root group=root mode=0600

# Set root password
# UPDATE mysql.user SET Password=PASSWORD('mysecret') WHERE User='root';
# FLUSH PRIVILEGES;

- name: Set root Password
  mysql_user: login_password={{ mysql_root_password }} check_implicit_admin=yes name=root host={{ item }} password={{ mysql_root_password }} state=present
  with_items:
    - localhost
    - 127.0.0.1
    - ::1

- name: Reload privilege tables
  command: 'mariadb -ne "{{ item }}"'
  with_items:
    - FLUSH PRIVILEGES
  changed_when: False
  when: run_travis is not defined

- name: Remove anonymous users
  command: 'mariadb -ne "{{ item }}"'
  with_items:
    - DELETE FROM mysql.user WHERE User=''
  changed_when: False
  when: run_travis is not defined

- name: Disallow root login remotely
  command: 'mariadb -ne "{{ item }}"'
  with_items:
    - DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1')
  changed_when: False
  when: run_travis is not defined

- name: Remove test database and access to it
  command: 'mariadb -ne "{{ item }}"'
  with_items:
    - DROP DATABASE IF EXISTS test
    - DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%'
  changed_when: False
  when: run_travis is not defined

- name: Reload privilege tables
  command: 'mariadb -ne "{{ item }}"'
  with_items:
    - FLUSH PRIVILEGES
  changed_when: False
  when: run_travis is not defined
