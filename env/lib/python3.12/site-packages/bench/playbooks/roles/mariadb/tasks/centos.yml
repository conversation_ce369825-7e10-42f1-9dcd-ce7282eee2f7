---
- name: Add repo file
  template: src=mariadb_centos.repo.j2 dest=/etc/yum.repos.d/mariadb.repo owner=root group=root mode=0644

- name: Install MariaDB
  yum:
    name:
      - MariaDB-server
      - MariaDB-client
    enablerepo: mariadb
    state: present

- name: Install MySQLdb Python package for secure installations.
  yum:
    name:
      - MySQL-python
    state: present
  when: mysql_secure_installation and mysql_root_password is defined
