# Block IPs trying to use server as proxy.
[Definition]
failregex = <HOST>.*\" 400
	<HOST>.*"[A-Z]* /(cms|muieblackcat|db|cpcommerce|cgi-bin|wp-login|joomla|awstatstotals|wp-content|wp-includes|pma|phpmyadmin|myadmin|mysql|mysqladmin|sqladmin|mypma|admin|xampp|mysqldb|pmadb|phpmyadmin1|phpmyadmin2).*" 4[\d][\d]
	<HOST>.*".*supports_implicit_sdk_logging.*" 4[\d][\d]
	<HOST>.*".*activities?advertiser_tracking_enabled.*" 4[\d][\d]
	<HOST>.*".*/picture?type=normal.*" 4[\d][\d]
	<HOST>.*".*/announce.php?info_hash=.*" 4[\d][\d]

ignoreregex =