- name: Create swap space
  command: dd if=/dev/zero of=/extraswap bs=1M count={{swap_size_mb}}
  when: ansible_swaptotal_mb < 1

- name: Make swap
  command: mkswap /extraswap
  when: ansible_swaptotal_mb < 1

- name: Add to fstab
  action: lineinfile dest=/etc/fstab regexp="extraswap" line="/extraswap none swap sw 0 0" state=present
  when: ansible_swaptotal_mb < 1

- name: Turn swap on
  command: swapon -a
  when: ansible_swaptotal_mb < 1

- name: Set swapiness
  shell: echo 1 | tee /proc/sys/vm/swappiness