---
- name: Install deps
  yum:
    name:
      - policycoreutils-python
      - selinux-policy-devel
    state: present
  when: ansible_distribution == 'CentOS'

- name: Check enabled SELinux modules
  shell: semanage module -l
  register: enabled_modules
  when: ansible_distribution == 'CentOS'

- name: Copy frappe_selinux policy
  copy: src=frappe_selinux.te dest=/root/frappe_selinux.te
  register: dest_frappe_selinux_te
  when: ansible_distribution == 'CentOS'

- name: Compile frappe_selinux policy
  shell: "make -f /usr/share/selinux/devel/Makefile frappe_selinux.pp && semodule -i frappe_selinux.pp"
  args:
    chdir: /root/
  when: "ansible_distribution == 'CentOS' and enabled_modules.stdout.find('frappe_selinux') == -1 or dest_frappe_selinux_te.changed"
...