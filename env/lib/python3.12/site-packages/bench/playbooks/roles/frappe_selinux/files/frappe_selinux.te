module frappe_selinux 1.0;

require {
	type user_home_dir_t;
	type httpd_t;
	type user_home_t;
	type soundd_port_t;
	class tcp_socket name_connect;
	class lnk_file read;
	class dir { getattr search };
	class file { read open };
}

#============= httpd_t ==============

#!!!! This avc is allowed in the current policy
allow httpd_t soundd_port_t:tcp_socket name_connect;

#!!!! This avc is allowed in the current policy
allow httpd_t user_home_dir_t:dir search;

#!!!! This avc is allowed in the current policy
allow httpd_t user_home_t:dir { getattr search };

#!!!! This avc can be allowed using the boolean 'httpd_read_user_content'
allow httpd_t user_home_t:file open;

#!!!! This avc is allowed in the current policy
allow httpd_t user_home_t:file read;

#!!!! This avc is allowed in the current policy
allow httpd_t user_home_t:lnk_file read;