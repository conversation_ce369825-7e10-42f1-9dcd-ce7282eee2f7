---
  - name: Install yum packages
    yum:
      name:
        - redis
      state: present
    when: ansible_os_family == 'RedHat'

  # Prerequisite for Debian and Ubuntu
  - name: Install apt packages
    apt:
      pkg:
        - redis-server
      state: present
      force: yes
    when: ansible_distribution == 'Debian' or ansible_distribution == 'Ubuntu'

  # Prerequisite for MACOS
  - name: install prequisites for macos
    homebrew:
      name:
        - redis
      state: present
    when: ansible_distribution == 'MacOSX'
...