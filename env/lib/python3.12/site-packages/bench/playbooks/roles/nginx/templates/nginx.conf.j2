user {{ nginx_user }};
worker_processes  auto;
worker_rlimit_nofile 65535;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections {{ nginx_worker_connections or 2048 }};
    multi_accept on;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    tcp_nopush on;
    tcp_nodelay on;
    server_tokens off;

    # keepalive_timeout  10;
    # keepalive_requests 10;

    gzip on;
    gzip_disable "msie6";
    gzip_http_version 1.1;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
        application/atom+xml
        application/javascript
        application/json
        application/rss+xml
        application/vnd.ms-fontobject
        application/x-font-ttf
        application/font-woff
        application/x-web-app-manifest+json
        application/xhtml+xml
        application/xml
        font/opentype
        image/svg+xml
        image/x-icon
        text/css
        text/plain
        text/x-component
        ;

    server_names_hash_max_size 4096;

    open_file_cache max=65000 inactive=1m;
    open_file_cache_valid 5s;
    open_file_cache_min_uses 1;
    open_file_cache_errors on;

    ssl_protocols  SSLv3 TLSv1;
    ssl_ciphers     ECDHE-RSA-AES256-SHA384:AES256-SHA256:RC4:HIGH:!MD5:!aNULL:!EDH:!AESGCM;
    ssl_prefer_server_ciphers   on;

    client_max_body_size 50m;
    large_client_header_buffers 4 32k;

    proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=web-cache:8m max_size=1000m inactive=600m;

    include /etc/nginx/conf.d/*.conf;
}
