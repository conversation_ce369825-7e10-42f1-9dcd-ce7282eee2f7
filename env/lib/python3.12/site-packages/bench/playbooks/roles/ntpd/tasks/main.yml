---
- name: Install ntpd
  yum:
    name:
      - ntp
      - ntpdate
    state: present
  when: ansible_distribution == 'CentOS'

- name: Enable ntpd
  service: name=ntpd enabled=yes state=started
  when: ansible_distribution == 'CentOS'

- name: Install ntpd
  apt:
    pkg:
      - ntp
      - ntpdate
    state: present
  when: ansible_distribution == 'Debian' or ansible_distribution == 'Ubuntu'

- name: Enable ntpd
  service: name=ntp enabled=yes state=started
  when: ansible_distribution == 'Debian' or ansible_distribution == 'Ubuntu'
...