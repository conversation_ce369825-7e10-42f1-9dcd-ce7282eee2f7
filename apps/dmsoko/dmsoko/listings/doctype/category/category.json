{"actions": [], "allow_rename": 1, "autoname": "field:category_name", "creation": "2024-06-19 12:00:00.000000", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["category_name", "description", "parent_category", "column_break_4", "icon", "image", "is_active", "sort_order", "section_break_8", "meta_title", "meta_description", "meta_keywords"], "fields": [{"fieldname": "category_name", "fieldtype": "Data", "in_list_view": 1, "label": "Category Name", "reqd": 1, "unique": 1}, {"fieldname": "description", "fieldtype": "Text", "label": "Description"}, {"fieldname": "parent_category", "fieldtype": "Link", "label": "Parent Category", "options": "Category"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "icon", "fieldtype": "Data", "label": "Icon Class", "description": "CSS class for icon (e.g., fas fa-car)"}, {"fieldname": "image", "fieldtype": "Attach Image", "label": "Category Image"}, {"default": "1", "fieldname": "is_active", "fieldtype": "Check", "in_list_view": 1, "label": "Is Active"}, {"default": "0", "fieldname": "sort_order", "fieldtype": "Int", "in_list_view": 1, "label": "Sort Order"}, {"fieldname": "section_break_8", "fieldtype": "Section Break", "label": "SEO Settings"}, {"fieldname": "meta_title", "fieldtype": "Data", "label": "Meta Title"}, {"fieldname": "meta_description", "fieldtype": "Text", "label": "Meta Description"}, {"fieldname": "meta_keywords", "fieldtype": "Text", "label": "Meta Keywords"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-06-19 12:00:00.000000", "modified_by": "Administrator", "module": "Listings", "name": "Category", "app": "dms<PERSON>", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "<PERSON><PERSON><PERSON>", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "DMSoko User", "share": 1}], "quick_entry": 1, "sort_field": "sort_order", "sort_order": "ASC", "states": [], "track_changes": 1}