#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to manually create DMSoko DocTypes
"""

import frappe
import json
import os

def create_doctype_from_json(json_path):
    """Create a DocType from JSON file"""
    try:
        with open(json_path, 'r') as f:
            doctype_dict = json.load(f)
        
        # Check if DocType already exists
        if frappe.db.exists("DocType", doctype_dict.get("name")):
            print(f"✅ DocType '{doctype_dict.get('name')}' already exists")
            return True
            
        # Create new DocType
        doc = frappe.get_doc(doctype_dict)
        doc.flags.ignore_permissions = True
        doc.flags.ignore_mandatory = True
        doc.insert()
        
        print(f"✅ Created DocType: {doctype_dict.get('name')}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating DocType from {json_path}: {str(e)}")
        return False

def main():
    """Main function to create all DMSoko DocTypes"""
    
    # Initialize Frappe
    frappe.init(site='p1.site')
    frappe.connect()
    
    # List of DocType JSON files in order of dependency
    doctype_files = [
        # Core DocTypes first
        "apps/dmsoko/dmsoko/listings/doctype/category/category.json",
        "apps/dmsoko/dmsoko/listings/doctype/listing_image/listing_image.json",
        "apps/dmsoko/dmsoko/listings/doctype/listing/listing.json",
        
        # User management
        "apps/dmsoko/dmsoko/user_management/doctype/user_profile/user_profile.json",
        "apps/dmsoko/dmsoko/user_management/doctype/wishlist/wishlist.json",
        
        # Messaging
        "apps/dmsoko/dmsoko/messaging/doctype/conversation/conversation.json",
        "apps/dmsoko/dmsoko/messaging/doctype/message/message.json",
    ]
    
    print("Creating DMSoko DocTypes...")
    print("=" * 50)
    
    success_count = 0
    total_count = len(doctype_files)
    
    for json_file in doctype_files:
        if os.path.exists(json_file):
            if create_doctype_from_json(json_file):
                success_count += 1
        else:
            print(f"❌ File not found: {json_file}")
    
    print("=" * 50)
    print(f"Created {success_count}/{total_count} DocTypes successfully")
    
    # Commit the changes
    frappe.db.commit()
    print("✅ Changes committed to database")

if __name__ == "__main__":
    main()
