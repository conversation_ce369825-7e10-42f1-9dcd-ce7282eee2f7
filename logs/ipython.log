2025-06-20 08:22:45,368 INFO ipython === bench console session ===
2025-06-20 08:22:45,369 INFO ipython from frappe.core.doctype.data_import.data_import import import_doc
2025-06-20 08:22:45,369 INFO ipython import_doc("apps/dmsoko/dmsoko/fixtures/custom_role.json")
2025-06-20 08:22:45,370 INFO ipython import_doc("/home/<USER>/dev/personal/apps/dmsoko/dmsoko/fixtures/custom_role.json")
2025-06-20 08:22:45,370 INFO ipython import_doc("/home/<USER>/dev/personal/apps/dmsoko/dmsoko/fixtures/category.json")
2025-06-20 08:22:45,370 INFO ipython === session end ===
2025-06-20 08:25:52,010 INFO ipython === bench console session ===
2025-06-20 08:25:52,010 INFO ipython frappe.db.exists("DocType", "Category")
2025-06-20 08:25:52,011 INFO ipython print(frappe.db.exists("DocType", "Category"))
2025-06-20 08:25:52,011 INFO ipython frappe.get_all("DocType", filters={"module": "Listings"}, fields=["name"])
2025-06-20 08:25:52,011 INFO ipython frappe.get_installed_apps()
2025-06-20 08:25:52,011 INFO ipython === session end ===
2025-06-20 08:27:56,703 INFO ipython === bench console session ===
2025-06-20 08:27:56,703 INFO ipython print(frappe.db.exists("DocType", "Category"))
2025-06-20 08:27:56,703 INFO ipython frappe.get_all("DocType", filters={"app": "dmsoko"}, fields=["name", "module"])
2025-06-20 08:27:56,704 INFO ipython === session end ===
